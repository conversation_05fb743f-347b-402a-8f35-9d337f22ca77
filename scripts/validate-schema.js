/**
 * Schema Validation Script for Credit Chakra Platform
 * 
 * Validates existing Firestore data against defined schemas:
 * - MSME records validation
 * - Raw events validation
 * - Alerts validation
 * - Chakra score validation
 * - Consent ledger validation
 * - Comprehensive validation reports
 */

const firestoreManager = require('../firestore/init');
const { Validators } = require('../utils/validators');
const logger = require('../utils/logger');
require('dotenv').config();

class SchemaValidator {
    constructor() {
        this.collections = {
            msmes: process.env.COLLECTION_MSMES || 'msmes',
            raw_events: process.env.COLLECTION_RAW_EVENTS || 'raw_events',
            alerts: process.env.COLLECTION_ALERTS || 'alerts',
            chakra_score: process.env.COLLECTION_CHAKRA_SCORE || 'chakra_score',
            consent_ledger: process.env.COLLECTION_CONSENT_LEDGER || 'consent_ledger'
        };
        
        this.validationResults = {};
    }

    /**
     * Convert Firestore timestamps to JavaScript dates
     */
    convertFirestoreData(data) {
        const converted = { ...data };
        
        // Convert Firestore timestamps to Date objects
        for (const [key, value] of Object.entries(converted)) {
            if (value && typeof value === 'object' && value._seconds !== undefined) {
                converted[key] = new Date(value._seconds * 1000);
            }
        }
        
        return converted;
    }

    /**
     * Validate MSME collection
     */
    async validateMSMEs() {
        try {
            logger.logOperation('Validating MSME collection');
            
            const db = firestoreManager.getDb();
            const snapshot = await db.collection(this.collections.msmes).get();
            
            const results = {
                total: snapshot.size,
                valid: 0,
                invalid: 0,
                errors: []
            };

            snapshot.forEach(doc => {
                const data = this.convertFirestoreData(doc.data());
                const validation = Validators.validateMsme(data);
                
                if (validation.isValid) {
                    results.valid++;
                } else {
                    results.invalid++;
                    results.errors.push({
                        document_id: doc.id,
                        errors: validation.errors
                    });
                }
            });

            this.validationResults.msmes = results;
            logger.logSuccess('MSME validation completed', {
                total: results.total,
                valid: results.valid,
                invalid: results.invalid
            });

            return results;
        } catch (error) {
            logger.logError('MSME validation failed', error);
            throw error;
        }
    }

    /**
     * Validate raw events collection
     */
    async validateRawEvents() {
        try {
            logger.logOperation('Validating raw events collection');
            
            const db = firestoreManager.getDb();
            const snapshot = await db.collection(this.collections.raw_events).get();
            
            const results = {
                total: snapshot.size,
                valid: 0,
                invalid: 0,
                errors: [],
                event_types: {}
            };

            snapshot.forEach(doc => {
                const data = this.convertFirestoreData(doc.data());
                const validation = Validators.validateRawEvent(data);
                
                // Track event types
                const eventType = data.event_type || 'unknown';
                if (!results.event_types[eventType]) {
                    results.event_types[eventType] = { valid: 0, invalid: 0 };
                }
                
                if (validation.isValid) {
                    results.valid++;
                    results.event_types[eventType].valid++;
                } else {
                    results.invalid++;
                    results.event_types[eventType].invalid++;
                    results.errors.push({
                        document_id: doc.id,
                        event_type: eventType,
                        errors: validation.errors
                    });
                }
            });

            this.validationResults.raw_events = results;
            logger.logSuccess('Raw events validation completed', {
                total: results.total,
                valid: results.valid,
                invalid: results.invalid,
                event_types: Object.keys(results.event_types).length
            });

            return results;
        } catch (error) {
            logger.logError('Raw events validation failed', error);
            throw error;
        }
    }

    /**
     * Validate alerts collection
     */
    async validateAlerts() {
        try {
            logger.logOperation('Validating alerts collection');
            
            const db = firestoreManager.getDb();
            const snapshot = await db.collection(this.collections.alerts).get();
            
            const results = {
                total: snapshot.size,
                valid: 0,
                invalid: 0,
                errors: [],
                severity_breakdown: { high: 0, medium: 0, low: 0 },
                status_breakdown: {}
            };

            snapshot.forEach(doc => {
                const data = this.convertFirestoreData(doc.data());
                const validation = Validators.validateAlert(data);
                
                if (validation.isValid) {
                    results.valid++;
                    
                    // Track severity
                    const severity = data.severity || 'unknown';
                    if (results.severity_breakdown[severity] !== undefined) {
                        results.severity_breakdown[severity]++;
                    }
                    
                    // Track status
                    const status = data.status || 'unknown';
                    results.status_breakdown[status] = (results.status_breakdown[status] || 0) + 1;
                } else {
                    results.invalid++;
                    results.errors.push({
                        document_id: doc.id,
                        errors: validation.errors
                    });
                }
            });

            this.validationResults.alerts = results;
            logger.logSuccess('Alerts validation completed', {
                total: results.total,
                valid: results.valid,
                invalid: results.invalid
            });

            return results;
        } catch (error) {
            logger.logError('Alerts validation failed', error);
            throw error;
        }
    }

    /**
     * Validate chakra score collection
     */
    async validateChakraScores() {
        try {
            logger.logOperation('Validating chakra score collection');
            
            const db = firestoreManager.getDb();
            const snapshot = await db.collection(this.collections.chakra_score).get();
            
            const results = {
                total: snapshot.size,
                valid: 0,
                invalid: 0,
                errors: [],
                band_distribution: { A: 0, B: 0, C: 0, D: 0, E: 0 }
            };

            snapshot.forEach(doc => {
                const data = this.convertFirestoreData(doc.data());
                const validation = Validators.validateChakraScore(data);
                
                if (validation.isValid) {
                    results.valid++;
                    
                    // Track band distribution
                    const band = data.band || 'unknown';
                    if (results.band_distribution[band] !== undefined) {
                        results.band_distribution[band]++;
                    }
                } else {
                    results.invalid++;
                    results.errors.push({
                        document_id: doc.id,
                        errors: validation.errors
                    });
                }
            });

            this.validationResults.chakra_score = results;
            logger.logSuccess('Chakra score validation completed', {
                total: results.total,
                valid: results.valid,
                invalid: results.invalid
            });

            return results;
        } catch (error) {
            logger.logError('Chakra score validation failed', error);
            throw error;
        }
    }

    /**
     * Validate consent ledger collection
     */
    async validateConsentLedger() {
        try {
            logger.logOperation('Validating consent ledger collection');
            
            const db = firestoreManager.getDb();
            const snapshot = await db.collection(this.collections.consent_ledger).get();
            
            const results = {
                total: snapshot.size,
                valid: 0,
                invalid: 0,
                errors: [],
                consent_types: {},
                status_breakdown: {}
            };

            snapshot.forEach(doc => {
                const data = this.convertFirestoreData(doc.data());
                const validation = Validators.validateConsentLedger(data);
                
                if (validation.isValid) {
                    results.valid++;
                    
                    // Track consent types
                    const consentType = data.consent_type || 'unknown';
                    results.consent_types[consentType] = (results.consent_types[consentType] || 0) + 1;
                    
                    // Track status
                    const status = data.status || 'unknown';
                    results.status_breakdown[status] = (results.status_breakdown[status] || 0) + 1;
                } else {
                    results.invalid++;
                    results.errors.push({
                        document_id: doc.id,
                        errors: validation.errors
                    });
                }
            });

            this.validationResults.consent_ledger = results;
            logger.logSuccess('Consent ledger validation completed', {
                total: results.total,
                valid: results.valid,
                invalid: results.invalid
            });

            return results;
        } catch (error) {
            logger.logError('Consent ledger validation failed', error);
            throw error;
        }
    }

    /**
     * Run comprehensive validation on all collections
     */
    async validateAllCollections() {
        try {
            logger.logOperation('Starting comprehensive schema validation');
            
            // Initialize Firestore
            await firestoreManager.initialize();
            
            const startTime = new Date();
            
            // Validate each collection
            await this.validateMSMEs();
            await this.validateRawEvents();
            await this.validateAlerts();
            await this.validateChakraScores();
            await this.validateConsentLedger();
            
            const endTime = new Date();
            const duration = Math.round((endTime - startTime) / 1000);
            
            // Generate summary
            const summary = this.generateValidationSummary(duration);
            
            logger.logSuccess('Comprehensive validation completed', summary);
            
            return {
                success: true,
                summary,
                detailed_results: this.validationResults
            };
            
        } catch (error) {
            logger.logError('Comprehensive validation failed', error);
            throw error;
        }
    }

    /**
     * Generate validation summary
     */
    generateValidationSummary(duration) {
        const summary = {
            duration_seconds: duration,
            total_documents: 0,
            total_valid: 0,
            total_invalid: 0,
            collections: {}
        };

        for (const [collection, results] of Object.entries(this.validationResults)) {
            summary.total_documents += results.total;
            summary.total_valid += results.valid;
            summary.total_invalid += results.invalid;
            
            summary.collections[collection] = {
                total: results.total,
                valid: results.valid,
                invalid: results.invalid,
                validity_percentage: results.total > 0 ? Math.round((results.valid / results.total) * 100) : 0
            };
        }

        summary.overall_validity_percentage = summary.total_documents > 0 
            ? Math.round((summary.total_valid / summary.total_documents) * 100) 
            : 0;

        return summary;
    }

    /**
     * Generate detailed validation report
     */
    generateDetailedReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.generateValidationSummary(0),
            detailed_results: this.validationResults
        };

        return report;
    }
}

// Run validation if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            const validator = new SchemaValidator();
            
            console.log('\n🔍 Credit Chakra Schema Validation');
            console.log('===================================\n');
            
            const result = await validator.validateAllCollections();
            
            if (result.success) {
                console.log('✅ Schema validation completed successfully!\n');
                console.log('📊 Summary:');
                console.log(`   Total Documents: ${result.summary.total_documents}`);
                console.log(`   Valid Documents: ${result.summary.total_valid}`);
                console.log(`   Invalid Documents: ${result.summary.total_invalid}`);
                console.log(`   Overall Validity: ${result.summary.overall_validity_percentage}%\n`);
                
                console.log('📋 Collection Breakdown:');
                for (const [collection, stats] of Object.entries(result.summary.collections)) {
                    console.log(`   ${collection}: ${stats.valid}/${stats.total} (${stats.validity_percentage}%)`);
                }
                
                // Show errors if any
                const hasErrors = Object.values(result.detailed_results).some(r => r.invalid > 0);
                if (hasErrors) {
                    console.log('\n⚠️  Validation Errors Found:');
                    for (const [collection, results] of Object.entries(result.detailed_results)) {
                        if (results.invalid > 0) {
                            console.log(`   ${collection}: ${results.invalid} invalid documents`);
                            results.errors.slice(0, 3).forEach(error => {
                                console.log(`     - ${error.document_id}: ${error.errors?.[0]?.message || 'Validation error'}`);
                            });
                            if (results.errors.length > 3) {
                                console.log(`     ... and ${results.errors.length - 3} more errors`);
                            }
                        }
                    }
                }
                
                process.exit(0);
            } else {
                console.error('❌ Schema validation failed');
                process.exit(1);
            }
        } catch (error) {
            console.error('💥 Fatal error during validation:', error);
            process.exit(1);
        }
    })();
}

module.exports = SchemaValidator;
