#!/usr/bin/env ts-node

/**
 * Test Script for Firebase Callable Function
 *
 * Tests the generateScoreCallable function directly without deploying to Firebase
 */

import { generateChakraScore } from '../functions/scoreEngine';

/**
 * Test the score engine function with different scenarios
 */
async function testScoreEngine() {
    console.log('\n🧪 Testing Chakra Score Engine Core Function');
    console.log('='.repeat(50));

    // Test 1: Valid MSME with alerts
    console.log('\n📋 Test 1: Valid MSME with alerts');
    console.log('-'.repeat(35));
    try {
        const result1 = await generateChakraScore('069d1acf-7932-41a4-8d58-57de4cb8e5a4');

        console.log(`✅ Success: Score generated`);
        console.log(`📊 Score: ${result1.score}/100`);
        console.log(`🎨 Band: ${result1.chakra_band.toUpperCase()}`);
        console.log(`📈 Alert Count: ${result1.input_alert_count}`);
        console.log(`🏷️  Top Drivers: ${result1.top_drivers.join(', ') || 'None'}`);
    } catch (error) {
        console.error(`❌ Error: ${error}`);
    }

    // Test 2: Valid MSME with no alerts
    console.log('\n📋 Test 2: Valid MSME with no alerts');
    console.log('-'.repeat(35));
    try {
        const result2 = await generateChakraScore('152f2dff-96ad-4031-ab92-35f0c0597139');

        console.log(`✅ Success: Score generated`);
        console.log(`📊 Score: ${result2.score}/100`);
        console.log(`🎨 Band: ${result2.chakra_band.toUpperCase()}`);
        console.log(`📈 Alert Count: ${result2.input_alert_count}`);
        console.log(`🏷️  Top Drivers: ${result2.top_drivers.join(', ') || 'None'}`);
    } catch (error) {
        console.error(`❌ Error: ${error}`);
    }

    // Test 3: Invalid MSME ID
    console.log('\n📋 Test 3: Invalid MSME ID');
    console.log('-'.repeat(25));
    try {
        const result3 = await generateChakraScore('INVALID_MSME_ID');
        console.log(`✅ Success: Score generated`);
        console.log(`📊 Score: ${result3.score}/100`);
    } catch (error) {
        console.log(`❌ Expected Error: ${(error as Error).message}`);
    }

    console.log('\n✨ All tests completed!');
}

// Run the tests
if (require.main === module) {
    testScoreEngine()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error('Test execution failed:', error);
            process.exit(1);
        });
}
