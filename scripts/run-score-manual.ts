#!/usr/bin/env ts-node

/**
 * Manual Testing Script for Chakra Score Engine v0.1
 *
 * Command-line interface for testing score generation with comprehensive logging
 * Usage: npm run test-score -- --msme-id=MSME_VINIT
 */

import * as admin from 'firebase-admin';
import { generateChakraScore } from '../functions/scoreEngine';
import * as path from 'path';

// Initialize Firebase Admin SDK
const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');
const serviceAccount = require(serviceAccountPath);

if (!admin.apps.length) {
    admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: 'credit-chakra-in'
    });
}

const db = admin.firestore();

/**
 * Parse command line arguments
 */
function parseArguments(): { msmeId?: string } {
    const args = process.argv.slice(2);
    const result: { msmeId?: string } = {};

    for (const arg of args) {
        if (arg.startsWith('--msme-id=')) {
            result.msmeId = arg.split('=')[1];
        }
    }

    return result;
}

/**
 * Display usage information
 */
function showUsage() {
    console.log('\n📋 Chakra Score Engine v0.1 - Manual Testing Tool');
    console.log('='.repeat(55));
    console.log('\nUsage:');
    console.log('  npm run test-score -- --msme-id=<MSME_ID>');
    console.log('\nExample:');
    console.log('  npm run test-score -- --msme-id=MSME_VINIT');
    console.log('\nOptions:');
    console.log('  --msme-id    Required. The MSME ID to calculate score for');
    console.log('');
}

/**
 * Fetch and display MSME information
 */
async function displayMSMEInfo(msmeId: string): Promise<void> {
    console.log('\n🏢 MSME Information');
    console.log('-'.repeat(30));

    try {
        const msmeDoc = await db.collection('msmes').doc(msmeId).get();

        if (!msmeDoc.exists) {
            console.log(`❌ MSME with ID ${msmeId} not found`);
            return;
        }

        const msmeData = msmeDoc.data();
        console.log(`✅ MSME Found: ${msmeData?.name || 'Unknown'}`);
        console.log(`   GSTIN: ${msmeData?.gstin || 'N/A'}`);
        console.log(`   Status: ${msmeData?.status || 'N/A'}`);
        console.log(`   Business Type: ${msmeData?.business_type || 'N/A'}`);
        console.log(`   Current Chakra Score: ${msmeData?.chakra_score || 'N/A'}`);
        console.log(`   Current Chakra Band: ${msmeData?.chakra_band || 'N/A'}`);

    } catch (error) {
        console.error(`❌ Error fetching MSME info: ${error}`);
    }
}

/**
 * Fetch and display alert information
 */
async function displayAlertInfo(msmeId: string): Promise<void> {
    console.log('\n🚨 Alert Analysis');
    console.log('-'.repeat(30));

    try {
        // Get all alerts for the MSME
        const allAlertsSnapshot = await db.collection('alerts')
            .where('msme_id', '==', msmeId)
            .get();

        // Get all alerts and filter unresolved ones
        const allAlertsForMsme = await db.collection('alerts')
            .where('msme_id', '==', msmeId)
            .get();

        const unresolvedAlerts: any[] = [];
        allAlertsForMsme.forEach(doc => {
            const alert = doc.data();
            if (alert.status !== 'resolved') {
                unresolvedAlerts.push(alert);
            }
        });

        console.log(`📊 Total Alerts: ${allAlertsSnapshot.size}`);
        console.log(`⚠️  Unresolved Alerts: ${unresolvedAlerts.length}`);

        if (unresolvedAlerts.length > 0) {
            console.log('\n📋 Unresolved Alert Details:');

            const severityCounts = { high: 0, medium: 0, low: 0 };
            const alertTypes: string[] = [];

            unresolvedAlerts.forEach(alert => {
                severityCounts[alert.severity as keyof typeof severityCounts]++;

                if (!alertTypes.includes(alert.alert_type)) {
                    alertTypes.push(alert.alert_type);
                }

                console.log(`   • ${alert.alert_type} (${alert.severity}) - ${alert.status}`);
                console.log(`     Message: ${alert.message.substring(0, 80)}...`);
            });

            console.log('\n📈 Severity Breakdown:');
            console.log(`   High: ${severityCounts.high} alerts (-${severityCounts.high * 10} points)`);
            console.log(`   Medium: ${severityCounts.medium} alerts (-${severityCounts.medium * 7} points)`);
            console.log(`   Low: ${severityCounts.low} alerts (-${severityCounts.low * 5} points)`);

            const totalDeductions = (severityCounts.high * 10) + (severityCounts.medium * 7) + (severityCounts.low * 5);
            console.log(`   Total Expected Deductions: -${totalDeductions} points`);
            console.log(`   Expected Final Score: ${Math.max(0, 100 - totalDeductions)}`);
        }

    } catch (error) {
        console.error(`❌ Error fetching alert info: ${error}`);
    }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
    console.log('\n🚀 Starting Chakra Score Engine Manual Test');
    console.log('='.repeat(50));
    console.log(`⏰ Timestamp: ${new Date().toISOString()}`);

    const { msmeId } = parseArguments();

    if (!msmeId) {
        showUsage();
        process.exit(1);
    }

    console.log(`🎯 Target MSME ID: ${msmeId}`);

    try {
        // Step 1: Display MSME information
        await displayMSMEInfo(msmeId);

        // Step 2: Display alert information
        await displayAlertInfo(msmeId);

        // Step 3: Generate score
        console.log('\n⚡ Score Generation Process');
        console.log('-'.repeat(35));
        console.log('🔄 Initiating score calculation...');

        const startTime = Date.now();
        const scoreResult = await generateChakraScore(msmeId);
        const endTime = Date.now();

        // Step 4: Display results
        console.log('\n🎉 Score Generation Complete!');
        console.log('='.repeat(35));
        console.log(`📊 Final Score: ${scoreResult.score}/100`);
        console.log(`🎨 Risk Band: ${scoreResult.chakra_band.toUpperCase()}`);
        console.log(`📈 Input Alert Count: ${scoreResult.input_alert_count}`);
        console.log(`🏷️  Top Drivers: ${scoreResult.top_drivers.join(', ') || 'None'}`);
        console.log(`📅 Generated At: ${scoreResult.generated_at.toDate().toISOString()}`);
        console.log(`⚡ Processing Time: ${endTime - startTime}ms`);
        console.log(`🔖 Version: ${scoreResult.version}`);

        // Step 5: Verify data was stored
        console.log('\n🔍 Verification');
        console.log('-'.repeat(20));

        const scoreQuery = await db.collection('chakra_score')
            .where('msme_id', '==', msmeId)
            .limit(5)
            .get();

        const auditQuery = await db.collection('score_audit')
            .where('msme_id', '==', msmeId)
            .limit(5)
            .get();

        console.log(`✅ Score record stored: ${!scoreQuery.empty}`);
        console.log(`✅ Audit record stored: ${!auditQuery.empty}`);

        console.log('\n✨ Test completed successfully!');

    } catch (error) {
        console.error('\n❌ Test failed with error:');
        console.error(error);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error('Script execution failed:', error);
            process.exit(1);
        });
}
