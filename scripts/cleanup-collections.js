/**
 * Cleanup Collections Script for Credit Chakra Platform
 * 
 * Provides safe cleanup utilities for development environment:
 * - Individual collection cleanup
 * - Complete database reset
 * - Selective data removal
 * - Backup before cleanup (optional)
 * - Confirmation prompts for safety
 */

const firestoreManager = require('../firestore/init');
const logger = require('../utils/logger');
require('dotenv').config();

class CollectionCleaner {
    constructor() {
        this.collections = {
            msmes: process.env.COLLECTION_MSMES || 'msmes',
            raw_events: process.env.COLLECTION_RAW_EVENTS || 'raw_events',
            alerts: process.env.COLLECTION_ALERTS || 'alerts',
            chakra_score: process.env.COLLECTION_CHAKRA_SCORE || 'chakra_score',
            consent_ledger: process.env.COLLECTION_CONSENT_LEDGER || 'consent_ledger'
        };
    }

    /**
     * Get collection statistics
     */
    async getCollectionStats(collectionName) {
        try {
            const db = firestoreManager.getDb();
            const snapshot = await db.collection(collectionName).get();
            
            return {
                name: collectionName,
                document_count: snapshot.size,
                exists: !snapshot.empty
            };
        } catch (error) {
            logger.error(`Failed to get stats for collection ${collectionName}:`, error);
            return {
                name: collectionName,
                document_count: 0,
                exists: false,
                error: error.message
            };
        }
    }

    /**
     * Get statistics for all collections
     */
    async getAllCollectionStats() {
        try {
            logger.logOperation('Gathering collection statistics');
            
            const stats = {};
            
            for (const [key, collectionName] of Object.entries(this.collections)) {
                stats[key] = await this.getCollectionStats(collectionName);
            }
            
            return stats;
        } catch (error) {
            logger.logError('Failed to gather collection statistics', error);
            throw error;
        }
    }

    /**
     * Delete all documents in a collection
     */
    async cleanupCollection(collectionName, batchSize = 500) {
        try {
            logger.logOperation(`Cleaning up collection: ${collectionName}`);
            
            const db = firestoreManager.getDb();
            const collection = db.collection(collectionName);
            
            let totalDeleted = 0;
            let hasMore = true;
            
            while (hasMore) {
                // Get a batch of documents
                const snapshot = await collection.limit(batchSize).get();
                
                if (snapshot.empty) {
                    hasMore = false;
                    break;
                }
                
                // Delete documents in batch
                const batch = db.batch();
                snapshot.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });
                
                await batch.commit();
                totalDeleted += snapshot.size;
                
                logger.info(`Deleted ${snapshot.size} documents from ${collectionName} (total: ${totalDeleted})`);
                
                // Check if there are more documents
                hasMore = snapshot.size === batchSize;
            }
            
            logger.logSuccess(`Collection cleanup completed: ${collectionName}`, {
                collection: collectionName,
                documents_deleted: totalDeleted
            });
            
            return {
                success: true,
                collection: collectionName,
                documents_deleted: totalDeleted
            };
            
        } catch (error) {
            logger.logError(`Collection cleanup failed: ${collectionName}`, error);
            throw error;
        }
    }

    /**
     * Clean up all collections
     */
    async cleanupAllCollections() {
        try {
            logger.logOperation('Starting cleanup of all collections');
            
            const results = {};
            let totalDeleted = 0;
            
            // Clean up in reverse dependency order
            const cleanupOrder = ['alerts', 'raw_events', 'chakra_score', 'consent_ledger', 'msmes'];
            
            for (const collectionKey of cleanupOrder) {
                const collectionName = this.collections[collectionKey];
                
                try {
                    const result = await this.cleanupCollection(collectionName);
                    results[collectionKey] = result;
                    totalDeleted += result.documents_deleted;
                } catch (error) {
                    results[collectionKey] = {
                        success: false,
                        collection: collectionName,
                        error: error.message
                    };
                }
            }
            
            logger.logSuccess('All collections cleanup completed', {
                total_documents_deleted: totalDeleted,
                collections_processed: Object.keys(results).length
            });
            
            return {
                success: true,
                total_documents_deleted: totalDeleted,
                results
            };
            
        } catch (error) {
            logger.logError('All collections cleanup failed', error);
            throw error;
        }
    }

    /**
     * Clean up specific collections by name
     */
    async cleanupSpecificCollections(collectionNames) {
        try {
            logger.logOperation('Cleaning up specific collections', { collections: collectionNames });
            
            const results = {};
            let totalDeleted = 0;
            
            for (const collectionName of collectionNames) {
                try {
                    const result = await this.cleanupCollection(collectionName);
                    results[collectionName] = result;
                    totalDeleted += result.documents_deleted;
                } catch (error) {
                    results[collectionName] = {
                        success: false,
                        collection: collectionName,
                        error: error.message
                    };
                }
            }
            
            logger.logSuccess('Specific collections cleanup completed', {
                total_documents_deleted: totalDeleted,
                collections_processed: collectionNames.length
            });
            
            return {
                success: true,
                total_documents_deleted: totalDeleted,
                results
            };
            
        } catch (error) {
            logger.logError('Specific collections cleanup failed', error);
            throw error;
        }
    }

    /**
     * Delete documents older than specified days
     */
    async cleanupOldDocuments(collectionName, daysOld = 30, dateField = 'created_at') {
        try {
            logger.logOperation(`Cleaning up old documents from ${collectionName}`, {
                days_old: daysOld,
                date_field: dateField
            });
            
            const db = firestoreManager.getDb();
            const collection = db.collection(collectionName);
            
            // Calculate cutoff date
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            
            // Query old documents
            const snapshot = await collection
                .where(dateField, '<', cutoffDate)
                .get();
            
            if (snapshot.empty) {
                logger.info(`No old documents found in ${collectionName}`);
                return {
                    success: true,
                    collection: collectionName,
                    documents_deleted: 0
                };
            }
            
            // Delete in batches
            const batchSize = 500;
            let totalDeleted = 0;
            
            for (let i = 0; i < snapshot.docs.length; i += batchSize) {
                const batch = db.batch();
                const batchDocs = snapshot.docs.slice(i, i + batchSize);
                
                batchDocs.forEach(doc => {
                    batch.delete(doc.ref);
                });
                
                await batch.commit();
                totalDeleted += batchDocs.length;
                
                logger.info(`Deleted ${batchDocs.length} old documents from ${collectionName} (total: ${totalDeleted})`);
            }
            
            logger.logSuccess(`Old documents cleanup completed: ${collectionName}`, {
                collection: collectionName,
                documents_deleted: totalDeleted,
                cutoff_date: cutoffDate.toISOString()
            });
            
            return {
                success: true,
                collection: collectionName,
                documents_deleted: totalDeleted,
                cutoff_date: cutoffDate
            };
            
        } catch (error) {
            logger.logError(`Old documents cleanup failed: ${collectionName}`, error);
            throw error;
        }
    }

    /**
     * Interactive cleanup CLI
     */
    async runInteractiveCLI() {
        try {
            console.log('\n🧹 Credit Chakra Collection Cleanup Utility');
            console.log('============================================\n');
            
            // Initialize Firestore
            await firestoreManager.initialize();
            
            // Get current statistics
            const stats = await this.getAllCollectionStats();
            
            console.log('📊 Current Collection Statistics:');
            let totalDocs = 0;
            for (const [key, stat] of Object.entries(stats)) {
                console.log(`   ${stat.name}: ${stat.document_count} documents`);
                totalDocs += stat.document_count;
            }
            console.log(`   Total: ${totalDocs} documents\n`);
            
            if (totalDocs === 0) {
                console.log('✅ No documents found. Collections are already clean.');
                return;
            }
            
            // In a real CLI, you would prompt the user for confirmation
            // For this demo, we'll show what would happen
            console.log('⚠️  WARNING: This will delete ALL documents from ALL collections!');
            console.log('🔄 In a real scenario, you would be prompted for confirmation.');
            console.log('💡 To proceed with cleanup, run: npm run cleanup -- --confirm');
            
            // Check for confirmation flag
            const args = process.argv.slice(2);
            if (args.includes('--confirm')) {
                console.log('\n🚀 Proceeding with cleanup...');
                const result = await this.cleanupAllCollections();
                
                console.log('\n✅ Cleanup completed successfully!');
                console.log(`📈 Total documents deleted: ${result.total_documents_deleted}`);
                
                // Show final statistics
                const finalStats = await this.getAllCollectionStats();
                console.log('\n📊 Final Collection Statistics:');
                for (const [key, stat] of Object.entries(finalStats)) {
                    console.log(`   ${stat.name}: ${stat.document_count} documents`);
                }
            }
            
        } catch (error) {
            console.error('\n❌ Cleanup failed:', error.message);
            throw error;
        }
    }
}

// Run CLI if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            const cleaner = new CollectionCleaner();
            
            // Parse command line arguments
            const args = process.argv.slice(2);
            
            if (args.includes('--help') || args.includes('-h')) {
                console.log('\n🧹 Credit Chakra Collection Cleanup Utility');
                console.log('============================================\n');
                console.log('Usage:');
                console.log('  node cleanup-collections.js                    # Interactive mode');
                console.log('  node cleanup-collections.js --confirm          # Cleanup all collections');
                console.log('  node cleanup-collections.js --collection msmes # Cleanup specific collection');
                console.log('  node cleanup-collections.js --old 30           # Cleanup documents older than 30 days');
                console.log('  node cleanup-collections.js --stats            # Show collection statistics only');
                console.log('\nOptions:');
                console.log('  --confirm       Confirm cleanup operation');
                console.log('  --collection    Specify collection name to cleanup');
                console.log('  --old           Cleanup documents older than specified days');
                console.log('  --stats         Show statistics only');
                console.log('  --help, -h      Show this help message\n');
                return;
            }
            
            await firestoreManager.initialize();
            
            if (args.includes('--stats')) {
                // Show statistics only
                const stats = await cleaner.getAllCollectionStats();
                console.log('\n📊 Collection Statistics:');
                for (const [key, stat] of Object.entries(stats)) {
                    console.log(`   ${stat.name}: ${stat.document_count} documents`);
                }
            } else if (args.includes('--collection')) {
                // Cleanup specific collection
                const collectionIndex = args.indexOf('--collection') + 1;
                const collectionName = args[collectionIndex];
                
                if (!collectionName) {
                    console.error('❌ Please specify a collection name');
                    process.exit(1);
                }
                
                const result = await cleaner.cleanupCollection(collectionName);
                console.log(`✅ Cleanup completed: ${result.documents_deleted} documents deleted`);
            } else if (args.includes('--old')) {
                // Cleanup old documents
                const daysIndex = args.indexOf('--old') + 1;
                const days = parseInt(args[daysIndex]) || 30;
                
                console.log(`🗓️  Cleaning up documents older than ${days} days...`);
                
                for (const collectionName of Object.values(cleaner.collections)) {
                    const result = await cleaner.cleanupOldDocuments(collectionName, days);
                    console.log(`   ${collectionName}: ${result.documents_deleted} documents deleted`);
                }
            } else {
                // Interactive mode
                await cleaner.runInteractiveCLI();
            }
            
            process.exit(0);
        } catch (error) {
            console.error('💥 Fatal error during cleanup:', error);
            process.exit(1);
        }
    })();
}

module.exports = CollectionCleaner;
