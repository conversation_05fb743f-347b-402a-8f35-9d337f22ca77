{"environment":"development","error":"msme.onboarded_at.toDate is not a function","level":"error","message":"Operation failed: Consent ledger seeding failed","service":"credit-chakra-nodejs-utils","stack":"TypeError: msme.onboarded_at.toDate is not a function\n    at ConsentLedgerSeeder.generateConsentsForMSME (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-consent-ledger.js:134:44)\n    at ConsentLedgerSeeder.generateConsentsForAllMSMEs (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-consent-ledger.js:196:39)\n    at ConsentLedgerSeeder.seedConsentLedger (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-consent-ledger.js:223:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MasterSeeder.seedConsentLedger (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-all.js:150:28)\n    at async MasterSeeder.seedAll (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-all.js:231:21)\n    at async /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-all.js:321:28","timestamp":"2025-05-25 21:35:23"}
{"environment":"development","error":"Value for argument \"data\" is not a valid Firestore document. Cannot use \"undefined\" as a Firestore value (found in field \"revoked_at\"). If you want to ignore undefined values, enable `ignoreUndefinedProperties`.","level":"error","message":"Operation failed: Consent ledger seeding failed","service":"credit-chakra-nodejs-utils","stack":"Error: Value for argument \"data\" is not a valid Firestore document. Cannot use \"undefined\" as a Firestore value (found in field \"revoked_at\"). If you want to ignore undefined values, enable `ignoreUndefinedProperties`.\n    at validateUserInput (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/node_modules/@google-cloud/firestore/build/src/serializer.js:383:19)\n    at validateUserInput (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/node_modules/@google-cloud/firestore/build/src/serializer.js:375:13)\n    at validateDocumentData (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/node_modules/@google-cloud/firestore/build/src/write-batch.js:609:40)\n    at WriteBatch.set (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/node_modules/@google-cloud/firestore/build/src/write-batch.js:263:9)\n    at ConsentLedgerSeeder.seedConsentLedger (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-consent-ledger.js:262:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MasterSeeder.seedConsentLedger (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-all.js:150:28)\n    at async MasterSeeder.seedAll (/Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-all.js:231:21)\n    at async /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/firestore/seeds/seed-all.js:321:28","timestamp":"2025-05-25 21:37:49"}
