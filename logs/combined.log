{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:05"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:05"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:05"}
{"environment":"development","level":"info","message":"Firestore connection test successful","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:07"}
{"environment":"development","level":"info","message":"Operation: Starting complete Credit Chakra platform seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:12"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:12"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:12"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:12"}
{"environment":"development","level":"info","message":"Firestore connection test successful","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:14"}
{"alerts":true,"chakra_score":true,"consent_ledger":true,"environment":"development","level":"warn","message":"Existing data found in collections:","msmes":true,"raw_events":true,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"warn","message":"Proceeding with seeding - existing data may be overwritten","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Operation: Step 1: Seeding MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Operation: Starting MSME seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"collection":"msmes","count":5,"environment":"development","level":"info","message":"Seeding msmes","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"collection":"msmes","environment":"development","level":"info","message":"Operation successful: MSME seeding completed","seeded":5,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Operation successful: MSMEs seeding completed","seeded":5,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Operation: Step 2: Seeding Raw Events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Operation: Starting raw events seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:19"}
{"environment":"development","level":"info","message":"Found 11 existing MSMEs for event generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"info","message":"Generated 110 events for 11 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"collection":"raw_events","count":110,"environment":"development","level":"info","message":"Seeding raw_events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"warn","message":"60 invalid event records found and skipped","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:20"}
{"environment":"development","level":"info","message":"Committed batch 1 with 50 events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"collection":"raw_events","environment":"development","level":"info","message":"Operation successful: Raw events seeding completed","seeded":50,"service":"credit-chakra-nodejs-utils","skipped":60,"timestamp":"2025-05-25 21:35:21"}
{"environment":"development","level":"info","message":"Operation successful: Raw Events seeding completed","seeded":50,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","level":"info","message":"Operation: Step 3: Seeding Alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","level":"info","message":"Operation: Starting alerts seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","level":"info","message":"Found 43 trigger events for alert generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"collection":"alerts","count":15,"environment":"development","level":"info","message":"Seeding alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"acknowledged_by","label":"acknowledged_by","value":null},"message":"\"acknowledged_by\" must be a string","path":["acknowledged_by"],"type":"string.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"environment":"development","level":"warn","message":"15 invalid alert records found and skipped","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:21"}
{"breakdown":{"event_based":43,"manual":5},"collection":"alerts","environment":"development","level":"info","message":"Operation successful: Alerts seeding completed","seeded":0,"service":"credit-chakra-nodejs-utils","skipped":15,"timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Operation successful: Alerts seeding completed","seeded":0,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Operation: Step 4: Seeding Chakra Score History","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Operation: Starting chakra score history seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Found 11 existing MSMEs for score history generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Generated 143 score history records for 11 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"collection":"chakra_score","count":143,"environment":"development","level":"info","message":"Seeding chakra_score","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"score","label":"score","limit":300,"value":67},"message":"\"score\" must be greater than or equal to 300","path":["score"],"type":"number.min"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00123"},"message":"\"msme_id\" with value \"MSME_00123\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"score","label":"score","limit":300,"value":45},"message":"\"score\" must be greater than or equal to 300","path":["score"],"type":"number.min"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_00124"},"message":"\"msme_id\" with value \"MSME_00124\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"score","label":"score","limit":300,"value":74},"message":"\"score\" must be greater than or equal to 300","path":["score"],"type":"number.min"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_KAVITA"},"message":"\"msme_id\" with value \"MSME_KAVITA\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"score","label":"score","limit":300,"value":58},"message":"\"score\" must be greater than or equal to 300","path":["score"],"type":"number.min"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_PRECISION"},"message":"\"msme_id\" with value \"MSME_PRECISION\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"score","label":"score","limit":300,"value":80},"message":"\"score\" must be greater than or equal to 300","path":["score"],"type":"number.min"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_TEST_001"},"message":"\"msme_id\" with value \"MSME_TEST_001\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"score","label":"score","limit":300,"value":43},"message":"\"score\" must be greater than or equal to 300","path":["score"],"type":"number.min"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"msme_id","label":"msme_id","regex":{},"value":"MSME_VINIT"},"message":"\"msme_id\" with value \"MSME_VINIT\" fails to match the required pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i","path":["msme_id"],"type":"string.pattern.base"},{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","errors":[{"context":{"key":"previous_score","label":"previous_score","value":null},"message":"\"previous_score\" must be a number","path":["previous_score"],"type":"number.base"}],"level":"warn","message":"Schema validation failed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"warn","message":"88 invalid score records found and skipped","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:22"}
{"environment":"development","level":"info","message":"Committed batch 1 with 55 score records","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:23"}
{"collection":"chakra_score","environment":"development","level":"info","message":"Operation successful: Chakra score history seeding completed","months_of_history":12,"seeded":55,"service":"credit-chakra-nodejs-utils","skipped":88,"timestamp":"2025-05-25 21:35:23"}
{"environment":"development","level":"info","message":"Operation successful: Chakra Score History seeding completed","seeded":55,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:23"}
{"environment":"development","level":"info","message":"Operation: Step 5: Seeding Consent Ledger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:23"}
{"environment":"development","level":"info","message":"Operation: Starting consent ledger seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:23"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:23"}
{"environment":"development","level":"info","message":"Found 11 existing MSMEs for consent ledger generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:23"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:33"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:33"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:33"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:33"}
{"environment":"development","level":"info","message":"Operation: Gathering collection statistics","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:33"}
{"environment":"development","level":"info","message":"Operation: Starting cleanup of all collections","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:39"}
{"environment":"development","level":"info","message":"Operation: Cleaning up collection: alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:39"}
{"environment":"development","level":"info","message":"Deleted 8 documents from alerts (total: 8)","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:41"}
{"collection":"alerts","documents_deleted":8,"environment":"development","level":"info","message":"Operation successful: Collection cleanup completed: alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:41"}
{"environment":"development","level":"info","message":"Operation: Cleaning up collection: raw_events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:41"}
{"environment":"development","level":"info","message":"Deleted 58 documents from raw_events (total: 58)","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:42"}
{"collection":"raw_events","documents_deleted":58,"environment":"development","level":"info","message":"Operation successful: Collection cleanup completed: raw_events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:42"}
{"environment":"development","level":"info","message":"Operation: Cleaning up collection: chakra_score","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:42"}
{"environment":"development","level":"info","message":"Deleted 56 documents from chakra_score (total: 56)","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:43"}
{"collection":"chakra_score","documents_deleted":56,"environment":"development","level":"info","message":"Operation successful: Collection cleanup completed: chakra_score","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:43"}
{"environment":"development","level":"info","message":"Operation: Cleaning up collection: consent_ledger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:43"}
{"environment":"development","level":"info","message":"Deleted 1 documents from consent_ledger (total: 1)","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44"}
{"collection":"consent_ledger","documents_deleted":1,"environment":"development","level":"info","message":"Operation successful: Collection cleanup completed: consent_ledger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44"}
{"environment":"development","level":"info","message":"Operation: Cleaning up collection: msmes","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44"}
{"environment":"development","level":"info","message":"Deleted 11 documents from msmes (total: 11)","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44"}
{"collection":"msmes","documents_deleted":11,"environment":"development","level":"info","message":"Operation successful: Collection cleanup completed: msmes","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44"}
{"collections_processed":5,"environment":"development","level":"info","message":"Operation successful: All collections cleanup completed","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44","total_documents_deleted":134}
{"environment":"development","level":"info","message":"Operation: Gathering collection statistics","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:35:44"}
{"environment":"development","level":"info","message":"Operation: Starting complete Credit Chakra platform seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:37"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:37"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:37"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:37"}
{"environment":"development","level":"info","message":"Firestore connection test successful","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:40"}
{"environment":"development","level":"info","message":"Operation: Step 1: Seeding MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Operation: Starting MSME seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"collection":"msmes","count":5,"environment":"development","level":"info","message":"Seeding msmes","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"collection":"msmes","environment":"development","level":"info","message":"Operation successful: MSME seeding completed","seeded":5,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Operation successful: MSMEs seeding completed","seeded":5,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Operation: Step 2: Seeding Raw Events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Operation: Starting raw events seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:44"}
{"environment":"development","level":"info","message":"Found 5 existing MSMEs for event generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Generated 50 events for 5 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"collection":"raw_events","count":50,"environment":"development","level":"info","message":"Seeding raw_events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Committed batch 1 with 50 events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"collection":"raw_events","environment":"development","level":"info","message":"Operation successful: Raw events seeding completed","seeded":50,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Operation successful: Raw Events seeding completed","seeded":50,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Operation: Step 3: Seeding Alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Operation: Starting alerts seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:45"}
{"environment":"development","level":"info","message":"Found 46 trigger events for alert generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"collection":"alerts","count":15,"environment":"development","level":"info","message":"Seeding alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","errors":[{"context":{"key":"acknowledged_at","label":"acknowledged_at","value":null},"message":"\"acknowledged_at\" must be a valid date","path":["acknowledged_at"],"type":"date.base"},{"context":{"key":"resolved_at","label":"resolved_at","value":null},"message":"\"resolved_at\" must be a valid date","path":["resolved_at"],"type":"date.base"}],"level":"warn","message":"Schema validation failed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"environment":"development","level":"warn","message":"15 invalid alert records found and skipped","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:46"}
{"breakdown":{"event_based":46,"manual":5},"collection":"alerts","environment":"development","level":"info","message":"Operation successful: Alerts seeding completed","seeded":0,"service":"credit-chakra-nodejs-utils","skipped":15,"timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Operation successful: Alerts seeding completed","seeded":0,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Operation: Step 4: Seeding Chakra Score History","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Operation: Starting chakra score history seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Found 5 existing MSMEs for score history generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Generated 65 score history records for 5 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"collection":"chakra_score","count":65,"environment":"development","level":"info","message":"Seeding chakra_score","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:47"}
{"environment":"development","level":"info","message":"Committed batch 1 with 65 score records","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:48"}
{"collection":"chakra_score","environment":"development","level":"info","message":"Operation successful: Chakra score history seeding completed","months_of_history":12,"seeded":65,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:37:48"}
{"environment":"development","level":"info","message":"Operation successful: Chakra Score History seeding completed","seeded":65,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:48"}
{"environment":"development","level":"info","message":"Operation: Step 5: Seeding Consent Ledger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:48"}
{"environment":"development","level":"info","message":"Operation: Starting consent ledger seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:48"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:48"}
{"environment":"development","level":"info","message":"Found 5 existing MSMEs for consent ledger generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:37:49"}
{"environment":"development","level":"info","message":"Operation: Starting complete Credit Chakra platform seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:40"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:40"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:40"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:40"}
{"environment":"development","level":"info","message":"Firestore connection test successful","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:43"}
{"alerts":false,"chakra_score":true,"consent_ledger":false,"environment":"development","level":"warn","message":"Existing data found in collections:","msmes":true,"raw_events":true,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"warn","message":"Proceeding with seeding - existing data may be overwritten","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"info","message":"Operation: Step 1: Seeding MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"info","message":"Operation: Starting MSME seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"collection":"msmes","count":5,"environment":"development","level":"info","message":"Seeding msmes","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:47"}
{"collection":"msmes","environment":"development","level":"info","message":"Operation successful: MSME seeding completed","seeded":5,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Operation successful: MSMEs seeding completed","seeded":5,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Operation: Step 2: Seeding Raw Events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Operation: Starting raw events seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Found 10 existing MSMEs for event generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Generated 100 events for 10 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"collection":"raw_events","count":100,"environment":"development","level":"info","message":"Seeding raw_events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:48"}
{"environment":"development","level":"info","message":"Committed batch 1 with 100 events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:49"}
{"collection":"raw_events","environment":"development","level":"info","message":"Operation successful: Raw events seeding completed","seeded":100,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:38:49"}
{"environment":"development","level":"info","message":"Operation successful: Raw Events seeding completed","seeded":100,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:49"}
{"environment":"development","level":"info","message":"Operation: Step 3: Seeding Alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:49"}
{"environment":"development","level":"info","message":"Operation: Starting alerts seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:49"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:49"}
{"environment":"development","level":"info","message":"Found 50 trigger events for alert generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"collection":"alerts","count":15,"environment":"development","level":"info","message":"Seeding alerts","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:50"}
{"breakdown":{"event_based":50,"manual":5},"collection":"alerts","environment":"development","level":"info","message":"Operation successful: Alerts seeding completed","seeded":15,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Operation successful: Alerts seeding completed","seeded":15,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Operation: Step 4: Seeding Chakra Score History","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Operation: Starting chakra score history seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Found 10 existing MSMEs for score history generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Generated 130 score history records for 10 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"collection":"chakra_score","count":130,"environment":"development","level":"info","message":"Seeding chakra_score","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:51"}
{"environment":"development","level":"info","message":"Committed batch 1 with 130 score records","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"collection":"chakra_score","environment":"development","level":"info","message":"Operation successful: Chakra score history seeding completed","months_of_history":12,"seeded":130,"service":"credit-chakra-nodejs-utils","skipped":0,"timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"info","message":"Operation successful: Chakra Score History seeding completed","seeded":130,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"info","message":"Operation: Step 5: Seeding Consent Ledger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"info","message":"Operation: Starting consent ledger seeding","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"info","message":"Found 10 existing MSMEs for consent ledger generation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"info","message":"Generated 60 consent records for 10 MSMEs","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"collection":"consent_ledger","count":60,"environment":"development","level":"info","message":"Seeding consent_ledger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","errors":[{"context":{"key":"granted_by","label":"granted_by","value":null},"message":"\"granted_by\" must be a string","path":["granted_by"],"type":"string.base"}],"level":"warn","message":"Schema validation failed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"warn","message":"1 invalid consent records found and skipped","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"collection":"consent_ledger","environment":"development","level":"info","message":"Operation successful: Consent ledger seeding completed","seeded":59,"service":"credit-chakra-nodejs-utils","skipped":1,"timestamp":"2025-05-25 21:38:54"}
{"environment":"development","level":"info","message":"Operation successful: Consent Ledger seeding completed","seeded":59,"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"collections":{"alerts":{"seeded":15,"skipped":0,"success":true},"chakraScore":{"seeded":130,"skipped":0,"success":true},"consentLedger":{"seeded":59,"skipped":1,"success":true},"msmes":{"seeded":5,"skipped":0,"success":true},"rawEvents":{"seeded":100,"skipped":0,"success":true}},"duration_seconds":14,"environment":"development","level":"info","message":"Operation successful: Complete seeding process finished","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54","total_records_seeded":309,"total_records_skipped":1}
{"environment":"development","level":"info","message":"Operation: Verifying seeded data","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:54"}
{"alerts":{"count":15,"hasData":true},"chakra_score":{"count":195,"hasData":true},"consent_ledger":{"count":59,"hasData":true},"environment":"development","level":"info","message":"Operation successful: Data verification completed","msmes":{"count":10,"hasData":true},"raw_events":{"count":150,"hasData":true},"service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:38:59"}
{"environment":"development","level":"info","message":"Operation: Starting comprehensive schema validation","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:07"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:07"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:07"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:07"}
{"environment":"development","level":"info","message":"Operation: Validating MSME collection","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:07"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: MSME","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","invalid":0,"level":"info","message":"Operation successful: MSME validation completed","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09","total":10,"valid":10}
{"environment":"development","level":"info","message":"Operation: Validating raw events collection","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:09"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","event_types":8,"invalid":0,"level":"info","message":"Operation successful: Raw events validation completed","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10","total":150,"valid":150}
{"environment":"development","level":"info","message":"Operation: Validating alerts collection","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:10"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: Alert","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","invalid":0,"level":"info","message":"Operation successful: Alerts validation completed","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11","total":15,"valid":15}
{"environment":"development","level":"info","message":"Operation: Validating chakra score collection","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:11"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ChakraScore","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","invalid":0,"level":"info","message":"Operation successful: Chakra score validation completed","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13","total":195,"valid":195}
{"environment":"development","level":"info","message":"Operation: Validating consent ledger collection","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"debug","message":"Schema validation passed: ConsentLedger","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:13"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:51"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:51"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:51"}
{"environment":"development","event_types":["EMI_BOUNCE","GST_DELAY","CASHFLOW_DIP"],"level":"info","message":"Operation: Generating test events for trigger engine","msme_id":"069d1acf-7932-41a4-8d58-57de4cb8e5a4","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:53"}
{"count":3,"environment":"development","level":"info","message":"Operation: Ingesting batch events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:53"}
{"environment":"development","level":"info","message":"Firebase Admin SDK already initialized","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:53"}
{"environment":"development","level":"debug","message":"Schema validation passed: RawEvent","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:53"}
{"environment":"development","level":"info","message":"Committed batch 1 with 3 events","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:39:55"}
{"environment":"development","level":"info","message":"Initializing Firebase Admin SDK with service account: /Users/<USER>/Documents/Credit Chakra/Latest/credit-chakra-api/serviceAccountKey.json","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:40:02"}
{"environment":"development","level":"info","message":"Firebase Admin SDK initialized successfully","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:40:02"}
{"environment":"development","level":"info","message":"Connected to Firestore project: credit-chakra-in","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:40:02"}
{"environment":"development","level":"info","message":"Operation: Gathering collection statistics","service":"credit-chakra-nodejs-utils","timestamp":"2025-05-25 21:40:02"}
