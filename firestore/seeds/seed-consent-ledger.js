/**
 * Consent Ledger Seeding Script for Credit Chakra Platform
 *
 * Creates sample consent records for data processing compliance:
 * - Data processing consents
 * - Credit check permissions
 * - Bank statement access consents
 * - GST data access permissions
 * - Score calculation consents
 * - Alert notification preferences
 */

const { v4: uuidv4 } = require('uuid');
const firestoreManager = require('../init');
const { Validators } = require('../../utils/validators');
const logger = require('../../utils/logger');
require('dotenv').config();

class ConsentLedgerSeeder {
    constructor() {
        this.collectionName = process.env.COLLECTION_CONSENT_LEDGER || 'consent_ledger';
        this.msmeCollectionName = process.env.COLLECTION_MSMES || 'msmes';
    }

    /**
     * Get existing MSMEs from Firestore
     */
    async getExistingMSMEs() {
        try {
            const db = firestoreManager.getDb();
            const msmeSnapshot = await db.collection(this.msmeCollectionName).get();

            const msmes = [];
            msmeSnapshot.forEach(doc => {
                msmes.push({ id: doc.id, ...doc.data() });
            });

            logger.info(`Found ${msmes.length} existing MSMEs for consent ledger generation`);
            return msmes;
        } catch (error) {
            logger.error('Failed to fetch existing MSMEs:', error);
            throw error;
        }
    }

    /**
     * Generate consent templates
     */
    getConsentTemplates() {
        return [
            {
                consent_type: 'data_processing',
                purpose: 'Processing business data for credit assessment and risk analysis',
                data_categories: ['business_profile', 'financial_data', 'contact_information'],
                duration_months: 24,
                required: true
            },
            {
                consent_type: 'credit_check',
                purpose: 'Accessing credit bureau reports for creditworthiness evaluation',
                data_categories: ['credit_history', 'loan_details', 'payment_behavior'],
                duration_months: 12,
                required: true
            },
            {
                consent_type: 'bank_statement_access',
                purpose: 'Analyzing bank statements for cash flow and financial health assessment',
                data_categories: ['bank_transactions', 'account_balance', 'payment_patterns'],
                duration_months: 18,
                required: false
            },
            {
                consent_type: 'gst_data_access',
                purpose: 'Accessing GST filing data for business compliance verification',
                data_categories: ['gst_returns', 'tax_payments', 'filing_history'],
                duration_months: 36,
                required: true
            },
            {
                consent_type: 'score_calculation',
                purpose: 'Calculating and maintaining Chakra credit scores based on collected data',
                data_categories: ['aggregated_data', 'score_factors', 'risk_indicators'],
                duration_months: 24,
                required: true
            },
            {
                consent_type: 'alert_notifications',
                purpose: 'Sending alerts and notifications about credit events and score changes',
                data_categories: ['contact_preferences', 'notification_history'],
                duration_months: 12,
                required: false
            }
        ];
    }

    /**
     * Generate realistic IP addresses
     */
    generateRandomIP() {
        const ipRanges = [
            '103.21.244', // Indian IP range
            '117.239.195',
            '152.58.96',
            '203.192.12',
            '49.207.40'
        ];

        const baseIP = ipRanges[Math.floor(Math.random() * ipRanges.length)];
        const lastOctet = Math.floor(Math.random() * 254) + 1;
        return `${baseIP}.${lastOctet}`;
    }

    /**
     * Generate user agents
     */
    generateRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 14; Mobile; rv:120.0) Gecko/120.0 Firefox/120.0'
        ];

        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }

    /**
     * Generate consent records for a single MSME
     */
    generateConsentsForMSME(msme) {
        const consentTemplates = this.getConsentTemplates();
        const consents = [];
        const baseDate = msme.onboarded_at instanceof Date ? msme.onboarded_at :
                        (msme.onboarded_at && msme.onboarded_at.toDate ? msme.onboarded_at.toDate() : new Date());

        for (const template of consentTemplates) {
            // Determine consent status
            let status, grantedAt, revokedAt, expiresAt;
            const random = Math.random();

            if (template.required || random < 0.85) {
                // Granted consent (required consents or 85% of optional ones)
                status = 'granted';
                grantedAt = new Date(baseDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000); // Within 7 days of onboarding
                expiresAt = new Date(grantedAt.getTime() + template.duration_months * 30 * 24 * 60 * 60 * 1000);

                // 10% chance of revocation for non-required consents
                if (!template.required && Math.random() < 0.1) {
                    status = 'revoked';
                    revokedAt = new Date(grantedAt.getTime() + Math.random() * (Date.now() - grantedAt.getTime()));
                }

                // Check if consent has expired
                if (expiresAt < new Date()) {
                    status = 'expired';
                }
            } else {
                // Pending or not granted
                status = Math.random() < 0.5 ? 'pending' : 'granted';
                if (status === 'granted') {
                    grantedAt = new Date(baseDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
                    expiresAt = new Date(grantedAt.getTime() + template.duration_months * 30 * 24 * 60 * 60 * 1000);
                }
            }

            const consent = {
                consent_id: uuidv4(),
                msme_id: msme.msme_id,
                consent_type: template.consent_type,
                status: status,
                granted_at: grantedAt,
                revoked_at: revokedAt,
                expires_at: expiresAt,
                purpose: template.purpose,
                data_categories: template.data_categories,
                granted_by: grantedAt ? uuidv4() : null, // Random user ID who granted consent
                ip_address: this.generateRandomIP(),
                user_agent: this.generateRandomUserAgent(),
                created_at: new Date(baseDate.getTime() + Math.random() * 24 * 60 * 60 * 1000), // Within 24 hours of onboarding
                updated_at: revokedAt || new Date()
            };

            consents.push(consent);
        }

        return consents;
    }

    /**
     * Generate consent records for all MSMEs
     */
    async generateConsentsForAllMSMEs(msmes) {
        const allConsents = [];

        for (const msme of msmes) {
            const msmeConsents = this.generateConsentsForMSME(msme);
            allConsents.push(...msmeConsents);
        }

        logger.info(`Generated ${allConsents.length} consent records for ${msmes.length} MSMEs`);
        return allConsents;
    }

    /**
     * Seed consent ledger to Firestore
     */
    async seedConsentLedger() {
        try {
            logger.logOperation('Starting consent ledger seeding');

            // Initialize Firestore
            await firestoreManager.initialize();
            const db = firestoreManager.getDb();
            const collection = db.collection(this.collectionName);

            // Get existing MSMEs
            const msmes = await this.getExistingMSMEs();
            if (msmes.length === 0) {
                throw new Error('No MSMEs found. Please seed MSMEs first.');
            }

            // Generate consent records
            const consents = await this.generateConsentsForAllMSMEs(msmes);
            logger.logSeeding(this.collectionName, consents.length);

            // Validate consent records
            const validationResults = consents.map(consent => Validators.validateConsentLedger(consent));
            const validConsents = validationResults.filter(result => result.isValid).map(result => result.data);
            const invalidCount = validationResults.length - validConsents.length;

            if (invalidCount > 0) {
                logger.warn(`${invalidCount} invalid consent records found and skipped`);
            }

            // Batch write to Firestore
            const batch = db.batch();
            const admin = firestoreManager.getAdmin();

            for (const consent of validConsents) {
                const docRef = collection.doc(consent.consent_id);

                // Convert dates to Firestore timestamps and filter out undefined values
                const firestoreData = {
                    ...consent,
                    created_at: admin.firestore.Timestamp.fromDate(consent.created_at),
                    updated_at: admin.firestore.Timestamp.fromDate(consent.updated_at)
                };

                // Only add optional date fields if they exist
                if (consent.granted_at) {
                    firestoreData.granted_at = admin.firestore.Timestamp.fromDate(consent.granted_at);
                }

                if (consent.revoked_at) {
                    firestoreData.revoked_at = admin.firestore.Timestamp.fromDate(consent.revoked_at);
                }

                if (consent.expires_at) {
                    firestoreData.expires_at = admin.firestore.Timestamp.fromDate(consent.expires_at);
                }

                // Remove undefined values to prevent Firestore errors
                Object.keys(firestoreData).forEach(key => {
                    if (firestoreData[key] === undefined) {
                        delete firestoreData[key];
                    }
                });

                batch.set(docRef, firestoreData);
            }

            await batch.commit();

            logger.logSuccess('Consent ledger seeding completed', {
                collection: this.collectionName,
                seeded: validConsents.length,
                skipped: invalidCount
            });

            return {
                success: true,
                seeded: validConsents.length,
                skipped: invalidCount,
                consents: validConsents
            };

        } catch (error) {
            logger.logError('Consent ledger seeding failed', error);
            throw error;
        }
    }
}

// Run seeding if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            const seeder = new ConsentLedgerSeeder();
            const result = await seeder.seedConsentLedger();
            console.log('Consent ledger seeding completed:', result);
            process.exit(0);
        } catch (error) {
            console.error('Consent ledger seeding failed:', error);
            process.exit(1);
        }
    })();
}

module.exports = ConsentLedgerSeeder;
