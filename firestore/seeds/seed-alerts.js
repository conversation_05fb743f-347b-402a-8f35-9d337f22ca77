/**
 * Alerts Seeding Script for Credit Chakra Platform
 *
 * Creates sample alerts based on existing raw events:
 * - T-01 EMI_BOUNCE alerts with high severity (-10 score impact)
 * - T-04 GST_DELAY alerts with medium severity (-7 score impact)
 * - T-03 CASHFLOW_DIP alerts with varying severity (-5 to -10 score impact)
 */

const { v4: uuidv4 } = require('uuid');
const firestoreManager = require('../init');
const { Validators } = require('../../utils/validators');
const logger = require('../../utils/logger');
require('dotenv').config();

class AlertsSeeder {
    constructor() {
        this.collectionName = process.env.COLLECTION_ALERTS || 'alerts';
        this.eventsCollectionName = process.env.COLLECTION_RAW_EVENTS || 'raw_events';
        this.alertsCount = parseInt(process.env.SAMPLE_ALERTS_COUNT) || 15;
    }

    /**
     * Get existing raw events from Firestore
     */
    async getExistingEvents() {
        try {
            const db = firestoreManager.getDb();
            const eventsSnapshot = await db.collection(this.eventsCollectionName)
                .where('event_type', 'in', ['EMI_BOUNCE', 'GST_DELAY', 'CASHFLOW_DIP'])
                .limit(50)
                .get();

            const events = [];
            eventsSnapshot.forEach(doc => {
                events.push({ id: doc.id, ...doc.data() });
            });

            logger.info(`Found ${events.length} trigger events for alert generation`);
            return events;
        } catch (error) {
            logger.error('Failed to fetch existing events:', error);
            throw error;
        }
    }

    /**
     * Generate alert based on event type and data
     */
    generateAlertFromEvent(event) {
        const alertId = uuidv4();
        let triggerRule, alertType, severity, scoreImpact, message;

        switch (event.event_type) {
            case 'EMI_BOUNCE':
                triggerRule = 'T-01';
                alertType = 'EMI_BOUNCE_DETECTED';
                severity = 'high';
                scoreImpact = -10;
                message = `EMI bounce detected for loan account ${event.event_data.loan_account_number}. Amount: ₹${event.event_data.emi_amount}. Reason: ${event.event_data.bounce_reason}`;
                break;

            case 'GST_DELAY':
                triggerRule = 'T-04';
                alertType = 'GST_FILING_DELAY';
                severity = event.event_data.delay_days > 30 ? 'high' : event.event_data.delay_days > 15 ? 'medium' : 'low';
                scoreImpact = severity === 'high' ? -10 : severity === 'medium' ? -7 : -5;
                message = `GST filing delayed by ${event.event_data.delay_days} days for ${event.event_data.return_type} (${event.event_data.return_period}). Penalty: ₹${event.event_data.penalty_amount}`;
                break;

            case 'CASHFLOW_DIP':
                triggerRule = 'T-03';
                alertType = 'CASHFLOW_DETERIORATION';
                severity = event.event_data.dip_percentage > 50 ? 'high' : event.event_data.dip_percentage > 30 ? 'medium' : 'low';
                scoreImpact = severity === 'high' ? -10 : severity === 'medium' ? -7 : -5;
                message = `Significant cashflow dip detected: ${event.event_data.dip_percentage}% decrease in average balance over ${event.event_data.analysis_period}`;
                break;

            default:
                return null;
        }

        // Determine alert status based on creation time
        const daysSinceCreation = Math.floor((Date.now() - event.timestamp.toDate().getTime()) / (1000 * 60 * 60 * 24));
        let status = 'active';
        let acknowledgedAt = null;
        let acknowledgedBy = null;
        let resolvedAt = null;

        if (daysSinceCreation > 7) {
            const random = Math.random();
            if (random < 0.3) {
                status = 'resolved';
                acknowledgedAt = new Date(event.timestamp.toDate().getTime() + Math.random() * 3 * 24 * 60 * 60 * 1000);
                acknowledgedBy = uuidv4(); // Random user ID
                resolvedAt = new Date(acknowledgedAt.getTime() + Math.random() * 4 * 24 * 60 * 60 * 1000);
            } else if (random < 0.6) {
                status = 'acknowledged';
                acknowledgedAt = new Date(event.timestamp.toDate().getTime() + Math.random() * 2 * 24 * 60 * 60 * 1000);
                acknowledgedBy = uuidv4(); // Random user ID
            }
        }

        return {
            alert_id: alertId,
            msme_id: event.msme_id,
            event_id: event.event_id,
            trigger_rule: triggerRule,
            alert_type: alertType,
            severity: severity,
            score_impact: scoreImpact,
            message: message,
            status: status,
            created_at: new Date(event.timestamp.toDate().getTime() + Math.random() * 60 * 60 * 1000), // Within 1 hour of event
            acknowledged_at: acknowledgedAt,
            acknowledged_by: acknowledgedBy,
            resolved_at: resolvedAt,
            metadata: {
                auto_generated: true,
                trigger_engine_version: '1.0',
                event_source: event.source,
                processing_time_ms: Math.floor(Math.random() * 1000) + 100
            }
        };
    }

    /**
     * Generate additional manual alerts for testing
     */
    generateManualAlerts(events) {
        const manualAlerts = [];
        const alertTypes = [
            {
                type: 'CREDIT_SCORE_DROP',
                severity: 'medium',
                scoreImpact: -5,
                message: 'Credit score dropped by more than 20 points in the last month'
            },
            {
                type: 'MULTIPLE_INQUIRIES',
                severity: 'low',
                scoreImpact: -3,
                message: 'Multiple credit inquiries detected in short period'
            },
            {
                type: 'PAYMENT_PATTERN_CHANGE',
                severity: 'medium',
                scoreImpact: -7,
                message: 'Significant change in payment patterns detected'
            }
        ];

        // Generate a few manual alerts
        for (let i = 0; i < Math.min(5, events.length); i++) {
            const event = events[i];
            const alertTemplate = alertTypes[i % alertTypes.length];

            manualAlerts.push({
                alert_id: uuidv4(),
                msme_id: event.msme_id,
                event_id: event.event_id,
                trigger_rule: 'MANUAL',
                alert_type: alertTemplate.type,
                severity: alertTemplate.severity,
                score_impact: alertTemplate.scoreImpact,
                message: alertTemplate.message,
                status: 'active',
                created_at: new Date(),
                acknowledged_at: null,
                acknowledged_by: null,
                resolved_at: null,
                metadata: {
                    auto_generated: false,
                    created_by: uuidv4(), // Random user ID
                    manual_review: true
                }
            });
        }

        return manualAlerts;
    }

    /**
     * Seed alerts to Firestore
     */
    async seedAlerts() {
        try {
            logger.logOperation('Starting alerts seeding');

            // Initialize Firestore
            await firestoreManager.initialize();
            const db = firestoreManager.getDb();
            const collection = db.collection(this.collectionName);

            // Get existing events
            const events = await this.getExistingEvents();
            if (events.length === 0) {
                throw new Error('No trigger events found. Please seed raw events first.');
            }

            // Generate alerts from events
            const eventAlerts = events
                .map(event => this.generateAlertFromEvent(event))
                .filter(alert => alert !== null);

            // Generate additional manual alerts
            const manualAlerts = this.generateManualAlerts(events.slice(0, 5));

            const allAlerts = [...eventAlerts, ...manualAlerts];

            // Limit to specified count
            const alerts = allAlerts.slice(0, this.alertsCount);

            logger.logSeeding(this.collectionName, alerts.length);

            // Validate alerts
            const validationResults = alerts.map(alert => Validators.validateAlert(alert));
            const validAlerts = validationResults.filter(result => result.isValid).map(result => result.data);
            const invalidCount = validationResults.length - validAlerts.length;

            if (invalidCount > 0) {
                logger.warn(`${invalidCount} invalid alert records found and skipped`);
            }

            // Batch write to Firestore
            const batch = db.batch();
            const admin = firestoreManager.getAdmin();

            for (const alert of validAlerts) {
                const docRef = collection.doc(alert.alert_id);

                // Convert dates to Firestore timestamps and filter out undefined values
                const firestoreData = {
                    ...alert,
                    created_at: admin.firestore.Timestamp.fromDate(alert.created_at)
                };

                // Only add optional date fields if they exist
                if (alert.acknowledged_at) {
                    firestoreData.acknowledged_at = admin.firestore.Timestamp.fromDate(alert.acknowledged_at);
                }

                if (alert.resolved_at) {
                    firestoreData.resolved_at = admin.firestore.Timestamp.fromDate(alert.resolved_at);
                }

                // Remove undefined values to prevent Firestore errors
                Object.keys(firestoreData).forEach(key => {
                    if (firestoreData[key] === undefined) {
                        delete firestoreData[key];
                    }
                });

                batch.set(docRef, firestoreData);
            }

            await batch.commit();

            logger.logSuccess('Alerts seeding completed', {
                collection: this.collectionName,
                seeded: validAlerts.length,
                skipped: invalidCount,
                breakdown: {
                    event_based: eventAlerts.length,
                    manual: manualAlerts.length
                }
            });

            return {
                success: true,
                seeded: validAlerts.length,
                skipped: invalidCount,
                alerts: validAlerts
            };

        } catch (error) {
            logger.logError('Alerts seeding failed', error);
            throw error;
        }
    }
}

// Run seeding if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            const seeder = new AlertsSeeder();
            const result = await seeder.seedAlerts();
            console.log('Alerts seeding completed:', result);
            process.exit(0);
        } catch (error) {
            console.error('Alerts seeding failed:', error);
            process.exit(1);
        }
    })();
}

module.exports = AlertsSeeder;
