/**
 * Firebase Admin SDK Initialization for Credit Chakra Platform
 *
 * This module initializes Firebase Admin SDK using the existing serviceAccountKey.json
 * from the FastAPI project root. It provides Firestore database access for seeding,
 * development utilities, and trigger engine testing.
 */

const admin = require('firebase-admin');
const path = require('path');
require('dotenv').config();

const logger = require('../utils/logger');

class FirestoreManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
    }

    /**
     * Initialize Firebase Admin SDK with service account key
     */
    async initialize() {
        try {
            if (this.isInitialized) {
                logger.info('Firebase Admin SDK already initialized');
                return this.db;
            }

            // Resolve service account key path relative to current working directory
            const serviceAccountPath = path.resolve(process.cwd(), process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH);

            logger.info(`Initializing Firebase Admin SDK with service account: ${serviceAccountPath}`);

            // Check if service account key exists
            const fs = require('fs');
            if (!fs.existsSync(serviceAccountPath)) {
                throw new Error(`Service account key not found at: ${serviceAccountPath}`);
            }

            // Initialize Firebase Admin SDK
            admin.initializeApp({
                credential: admin.credential.cert(serviceAccountPath),
                projectId: process.env.FIREBASE_PROJECT_ID
            });

            this.db = admin.firestore();
            this.isInitialized = true;

            logger.info('Firebase Admin SDK initialized successfully');
            logger.info(`Connected to Firestore project: ${process.env.FIREBASE_PROJECT_ID}`);

            return this.db;
        } catch (error) {
            logger.error('Failed to initialize Firebase Admin SDK:', error);
            throw error;
        }
    }

    /**
     * Get Firestore database instance
     */
    getDb() {
        if (!this.isInitialized) {
            throw new Error('Firebase Admin SDK not initialized. Call initialize() first.');
        }
        return this.db;
    }

    /**
     * Get collection reference
     */
    getCollection(collectionName) {
        return this.getDb().collection(collectionName);
    }

    /**
     * Test database connection
     */
    async testConnection() {
        try {
            const db = this.getDb();

            // Test connection by getting server timestamp
            const testDoc = await db.collection('_test').doc('connection').set({
                timestamp: admin.firestore.FieldValue.serverTimestamp(),
                test: true
            });

            // Clean up test document
            await db.collection('_test').doc('connection').delete();

            logger.info('Firestore connection test successful');
            return true;
        } catch (error) {
            logger.error('Firestore connection test failed:', error);
            throw error;
        }
    }

    /**
     * Get Firestore admin instance for advanced operations
     */
    getAdmin() {
        return admin;
    }

    /**
     * Close Firebase connection (for cleanup)
     */
    async close() {
        try {
            if (this.isInitialized) {
                await admin.app().delete();
                this.isInitialized = false;
                this.db = null;
                logger.info('Firebase Admin SDK connection closed');
            }
        } catch (error) {
            logger.error('Error closing Firebase connection:', error);
            throw error;
        }
    }
}

// Create singleton instance
const firestoreManager = new FirestoreManager();

// Auto-initialize if this file is run directly
if (require.main === module) {
    (async () => {
        try {
            await firestoreManager.initialize();
            await firestoreManager.testConnection();
            logger.info('Firebase initialization test completed successfully');
            process.exit(0);
        } catch (error) {
            logger.error('Firebase initialization test failed:', error);
            process.exit(1);
        }
    })();
}

module.exports = firestoreManager;
