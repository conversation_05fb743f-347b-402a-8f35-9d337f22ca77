/**
 * <PERSON> Configuration for Credit Chakra Platform
 * 
 * Provides structured logging for development utilities, seeding operations,
 * and trigger engine testing with appropriate log levels and formatting.
 */

const winston = require('winston');
require('dotenv').config();

// Define log levels
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3
};

// Define log colors
const logColors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    debug: 'blue'
};

// Add colors to winston
winston.addColors(logColors);

// Create logger instance
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    levels: logLevels,
    format: winston.format.combine(
        winston.format.timestamp({
            format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { 
        service: 'credit-chakra-nodejs-utils',
        environment: process.env.NODE_ENV || 'development'
    },
    transports: [
        // Write all logs with level 'error' and below to error.log
        new winston.transports.File({ 
            filename: 'logs/error.log', 
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5
        }),
        
        // Write all logs to combined.log
        new winston.transports.File({ 
            filename: 'logs/combined.log',
            maxsize: 5242880, // 5MB
            maxFiles: 5
        })
    ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
                let log = `${timestamp} [${service}] ${level}: ${message}`;
                
                // Add metadata if present
                if (Object.keys(meta).length > 0) {
                    log += ` ${JSON.stringify(meta)}`;
                }
                
                return log;
            })
        )
    }));
}

// Create logs directory if it doesn't exist
const fs = require('fs');
const path = require('path');
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Helper functions for common logging patterns
logger.logOperation = (operation, details = {}) => {
    logger.info(`Operation: ${operation}`, details);
};

logger.logError = (operation, error, details = {}) => {
    logger.error(`Operation failed: ${operation}`, {
        error: error.message,
        stack: error.stack,
        ...details
    });
};

logger.logSuccess = (operation, details = {}) => {
    logger.info(`Operation successful: ${operation}`, details);
};

logger.logSeeding = (collection, count, details = {}) => {
    logger.info(`Seeding ${collection}`, {
        count,
        collection,
        ...details
    });
};

logger.logValidation = (schema, isValid, errors = []) => {
    if (isValid) {
        logger.debug(`Schema validation passed: ${schema}`);
    } else {
        logger.warn(`Schema validation failed: ${schema}`, { errors });
    }
};

module.exports = logger;
