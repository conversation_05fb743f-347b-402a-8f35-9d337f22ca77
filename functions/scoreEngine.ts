/**
 * Chakra Score Engine v0.1 for Credit Chakra Platform
 *
 * Calculates credit scores based on unresolved alerts and stores results in Firestore
 * with comprehensive audit trails. Implements the exact scoring algorithm specified:
 * - Base score: 100 points
 * - Deductions by severity: high (-10), medium (-7), low (-5)
 * - Risk bands: Green (71-100), <PERSON> (51-70), <PERSON> (0-50)
 */

import * as admin from 'firebase-admin';
import { v4 as uuidv4 } from 'uuid';

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
    const serviceAccount = require('../serviceAccountKey.json');
    admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: 'credit-chakra-in'
    });
}

const db = admin.firestore();

// TypeScript interfaces for data structures
export interface Alert {
    alert_id: string;
    msme_id: string;
    event_id: string;
    trigger_rule: string;
    alert_type: string;
    severity: 'high' | 'medium' | 'low';
    score_impact: number;
    message: string;
    status: 'active' | 'acknowledged' | 'resolved' | 'dismissed';
    created_at: admin.firestore.Timestamp;
    acknowledged_at?: admin.firestore.Timestamp | null;
    acknowledged_by?: string | null;
    resolved_at?: admin.firestore.Timestamp | null;
    metadata?: any;
}

export interface ChakraScoreResult {
    msme_id: string;
    score: number;
    chakra_band: 'green' | 'amber' | 'red';
    top_drivers: string[];
    version: 'v0.1';
    generated_at: admin.firestore.Timestamp;
    input_alert_count: number;
}

export interface ScoreAuditRecord {
    msme_id: string;
    calculation_id: string;
    input_alerts: Alert[];
    base_score: number;
    total_deductions: number;
    final_score: number;
    band_assigned: string;
    calculated_at: admin.firestore.Timestamp;
    version: 'v0.1';
}

/**
 * Calculate Chakra Score for a given MSME based on unresolved alerts
 * @param msmeId - The MSME ID to calculate score for
 * @returns Promise<ChakraScoreResult> - The calculated score result
 */
export async function generateChakraScore(msmeId: string): Promise<ChakraScoreResult> {
    console.log(`Starting Chakra Score calculation for MSME: ${msmeId}`);

    try {
        // Step 1: Validate MSME exists
        const msmeDoc = await db.collection('msmes').doc(msmeId).get();
        if (!msmeDoc.exists) {
            throw new Error(`MSME with ID ${msmeId} not found`);
        }

        console.log(`✓ MSME ${msmeId} found in database`);

        // Step 2: Query alerts for the MSME and filter unresolved ones
        console.log(`Querying alerts for MSME: ${msmeId}`);
        const alertsSnapshot = await db.collection('alerts')
            .where('msme_id', '==', msmeId)
            .get();

        const unresolvedAlerts: Alert[] = [];
        alertsSnapshot.forEach(doc => {
            const alert = doc.data() as Alert;
            // Filter out resolved alerts
            if (alert.status !== 'resolved') {
                unresolvedAlerts.push(alert);
            }
        });

        console.log(`✓ Found ${unresolvedAlerts.length} unresolved alerts`);

        // Step 3: Calculate score using exact algorithm
        const baseScore = 100;
        let totalDeductions = 0;
        const severityDeductions = { high: 10, medium: 7, low: 5 };
        const topDrivers: string[] = [];

        console.log(`Starting score calculation with base score: ${baseScore}`);

        for (const alert of unresolvedAlerts) {
            const deduction = severityDeductions[alert.severity];
            totalDeductions += deduction;

            // Track top drivers (alert types contributing to score reduction)
            if (!topDrivers.includes(alert.alert_type)) {
                topDrivers.push(alert.alert_type);
            }

            console.log(`  - Alert ${alert.alert_id}: ${alert.severity} severity, -${deduction} points`);
        }

        // Apply floor constraint (minimum score = 0)
        const finalScore = Math.max(0, baseScore - totalDeductions);

        console.log(`✓ Score calculation complete: ${baseScore} - ${totalDeductions} = ${finalScore}`);

        // Step 4: Map to risk bands
        let chakraBand: 'green' | 'amber' | 'red';
        if (finalScore >= 71) {
            chakraBand = 'green';
        } else if (finalScore >= 51) {
            chakraBand = 'amber';
        } else {
            chakraBand = 'red';
        }

        console.log(`✓ Risk band assigned: ${chakraBand} (score: ${finalScore})`);

        // Step 5: Prepare result objects
        const calculationId = uuidv4();
        const timestamp = admin.firestore.Timestamp.now();

        const scoreResult: ChakraScoreResult = {
            msme_id: msmeId,
            score: finalScore,
            chakra_band: chakraBand,
            top_drivers: topDrivers,
            version: 'v0.1',
            generated_at: timestamp,
            input_alert_count: unresolvedAlerts.length
        };

        const auditRecord: ScoreAuditRecord = {
            msme_id: msmeId,
            calculation_id: calculationId,
            input_alerts: unresolvedAlerts,
            base_score: baseScore,
            total_deductions: totalDeductions,
            final_score: finalScore,
            band_assigned: chakraBand,
            calculated_at: timestamp,
            version: 'v0.1'
        };

        // Step 6: Atomic write to Firestore (both score and audit records)
        console.log(`Writing results to Firestore with calculation ID: ${calculationId}`);

        const batch = db.batch();

        // Write to chakra_score collection
        const scoreDocRef = db.collection('chakra_score').doc();
        batch.set(scoreDocRef, scoreResult);

        // Write to score_audit collection
        const auditDocRef = db.collection('score_audit').doc(calculationId);
        batch.set(auditDocRef, auditRecord);

        await batch.commit();

        console.log(`✓ Successfully stored score and audit records`);
        console.log(`✓ Chakra Score calculation completed for MSME: ${msmeId}`);

        return scoreResult;

    } catch (error) {
        console.error(`✗ Error calculating Chakra Score for MSME ${msmeId}:`, error);
        throw error;
    }
}
