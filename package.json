{"name": "credit-chakra-nodejs-utils", "version": "1.0.0", "description": "Node.js development utilities for Credit Chakra platform - Firestore operations, seeding, and development tools", "main": "index.js", "scripts": {"setup:check": "node setup-check.js", "seed:all": "node firestore/seeds/seed-all.js", "seed:msmes": "node firestore/seeds/seed-msmes.js", "seed:events": "node firestore/seeds/seed-raw-events.js", "seed:alerts": "node firestore/seeds/seed-alerts.js", "seed:scores": "node firestore/seeds/seed-chakra-score.js", "seed:consent": "node firestore/seeds/seed-consent-ledger.js", "ingest:event": "node scripts/ingest-event.js", "validate:schema": "node scripts/validate-schema.js", "cleanup": "node scripts/cleanup-collections.js", "test:connection": "node firestore/init.js"}, "keywords": ["credit-chakra", "firestore", "firebase", "seeding", "development-tools", "trigger-engine"], "author": "Credit Chakra Team", "license": "ISC", "dependencies": {"firebase-admin": "^12.0.0", "dotenv": "^16.4.5", "uuid": "^9.0.1", "joi": "^17.12.2", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.1.0"}, "engines": {"node": ">=16.0.0"}}