{"name": "credit-chakra-nodejs-utils", "version": "1.0.0", "description": "Node.js development utilities for Credit Chakra platform - Firestore operations, seeding, and development tools", "main": "index.js", "scripts": {"setup:check": "node setup-check.js", "seed:all": "node firestore/seeds/seed-all.js", "seed:msmes": "node firestore/seeds/seed-msmes.js", "seed:events": "node firestore/seeds/seed-raw-events.js", "seed:alerts": "node firestore/seeds/seed-alerts.js", "seed:scores": "node firestore/seeds/seed-chakra-score.js", "seed:consent": "node firestore/seeds/seed-consent-ledger.js", "ingest:event": "node scripts/ingest-event.js", "validate:schema": "node scripts/validate-schema.js", "cleanup": "node scripts/cleanup-collections.js", "test:connection": "node firestore/init.js", "test-score": "ts-node scripts/run-score-manual.ts", "test-callable": "ts-node scripts/test-callable-function.ts", "test-boundary": "ts-node scripts/test-boundary-cases.ts", "build:functions": "tsc --project functions/tsconfig.json", "deploy:functions": "npm run build:functions && firebase deploy --only functions"}, "keywords": ["credit-chakra", "firestore", "firebase", "seeding", "development-tools", "trigger-engine"], "author": "Credit Chakra Team", "license": "ISC", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.5.0", "dotenv": "^16.4.5", "uuid": "^9.0.1", "joi": "^17.12.2", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.1.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "ts-node": "^10.9.2"}, "engines": {"node": ">=16.0.0"}}