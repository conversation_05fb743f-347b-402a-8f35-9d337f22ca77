# Credit Chakra Platform - Node.js Development Utilities

A comprehensive Node.js development environment for the Credit Chakra platform, providing Firestore operations, data seeding, and development utilities to complement the existing FastAPI backend.

## 🏗️ Architecture Overview

This Node.js utilities package integrates seamlessly with the Credit Chakra platform's FastAPI backend, providing:

- **Data Infrastructure Bootstrap**: Firestore seeding and management
- **Trigger Engine Testing**: Event ingestion for T-01, T-04, T-03 triggers
- **Development Utilities**: Schema validation, cleanup, and monitoring
- **Firebase Admin SDK Integration**: Server-side Firestore operations
- **Role-Based Access Control**: Security rules (commented for development)

## 📁 Project Structure

```
nodejs-utils/
├── package.json                      # Dependencies and scripts
├── .env                             # Environment configuration
├── firestore/
│   ├── init.js                      # Firebase Admin SDK initialization
│   ├── rules/
│   │   └── firestore.rules          # Security rules (RBAC commented for dev)
│   └── seeds/
│       ├── seed-msmes.js            # MSME sample data (5 realistic records)
│       ├── seed-raw-events.js       # Raw events for trigger testing
│       ├── seed-alerts.js           # Sample alerts with severity levels
│       ├── seed-chakra-score.js     # Chakra score history records
│       ├── seed-consent-ledger.js   # Consent ledger entries
│       └── seed-all.js              # Master seed script
├── scripts/
│   ├── ingest-event.js              # Event ingestion for trigger testing
│   ├── validate-schema.js           # Schema validation utility
│   └── cleanup-collections.js      # Development cleanup utility
├── utils/
│   ├── logger.js                    # Winston logging utility
│   └── validators.js                # Joi data validation schemas
└── README.md                        # This documentation
```

## 🚀 Quick Start

### Prerequisites

1. **Node.js**: Version 16.0.0 or higher
2. **Firebase Project**: Credit Chakra platform Firebase project
3. **Service Account Key**: `serviceAccountKey.json` in the parent directory
4. **Environment Setup**: Configured `.env` file

### Installation

```bash
# Navigate to nodejs-utils directory
cd nodejs-utils

# Install dependencies
npm install

# Test Firebase connection
npm run test:connection
```

### Configuration

1. **Environment Variables**: Update `.env` file with your Firebase project details:
   ```env
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_SERVICE_ACCOUNT_KEY_PATH=../serviceAccountKey.json
   ```

2. **Service Account Key**: Ensure `serviceAccountKey.json` exists in the parent directory:
   ```
   credit-chakra-api/
   ├── serviceAccountKey.json  # Firebase service account key
   └── nodejs-utils/           # This project
   ```

## 📊 Data Seeding

### Complete Database Seeding

```bash
# Seed all collections with sample data
npm run seed:all

# Seed with options
npm run seed:all -- --skip-existing-check --continue-on-error
```

### Individual Collection Seeding

```bash
# Seed MSMEs (base entities)
npm run seed:msmes

# Seed raw events for trigger testing
npm run seed:events

# Seed alerts based on events
npm run seed:alerts

# Seed chakra score history
npm run seed:scores

# Seed consent ledger for compliance
npm run seed:consent
```

### Sample Data Overview

- **MSMEs**: 5 realistic Indian businesses with valid GSTIN, addresses, and contact details
- **Raw Events**: 50+ events including T-01 EMI_BOUNCE, T-04 GST_DELAY, T-03 CASHFLOW_DIP
- **Alerts**: 15+ alerts with severity levels (high: -10, medium: -7, low: -5 score impact)
- **Chakra Scores**: 12 months of score history showing evolution over time
- **Consent Ledger**: Compliance records for data processing permissions

## 🔧 Development Utilities

### Event Ingestion (Trigger Engine Testing)

```bash
# Interactive event ingestion
npm run ingest:event

# Ingest specific events for an MSME
node scripts/ingest-event.js <msme-id> EMI_BOUNCE GST_DELAY

# Generate test events for trigger engine
node scripts/ingest-event.js
```

### Schema Validation

```bash
# Validate all collections against schemas
npm run validate:schema

# Show detailed validation report
node scripts/validate-schema.js
```

### Database Cleanup

```bash
# Interactive cleanup (with confirmation prompts)
npm run cleanup

# Show collection statistics only
node scripts/cleanup-collections.js --stats

# Cleanup specific collection
node scripts/cleanup-collections.js --collection msmes --confirm

# Cleanup old documents (30+ days)
node scripts/cleanup-collections.js --old 30

# Complete cleanup (with confirmation)
node scripts/cleanup-collections.js --confirm
```

## 🎯 Trigger Engine Integration

The Node.js utilities support the existing Python polling-based Trigger Engine:

### Supported Trigger Rules

1. **T-01 EMI_BOUNCE**: High severity (-10 score impact)
   - Loan EMI bounce events
   - Bank account insufficient funds
   - Consecutive bounce tracking

2. **T-04 GST_DELAY**: Variable severity (-5 to -10 score impact)
   - GST filing delays
   - Penalty calculations
   - Compliance tracking

3. **T-03 CASHFLOW_DIP**: Variable severity (-5 to -10 score impact)
   - Cash flow deterioration
   - Balance trend analysis
   - Consecutive low-balance days

### Event Processing Flow

```
Raw Event → Validation → Firestore → Trigger Engine (Python) → Alert Generation
```

## 🔐 Security & RBAC

### Role-Based Access Control

The platform supports four user roles (currently commented out for development):

- **Admin**: Full read/write access to all collections
- **RM (Relationship Manager)**: Read access to assigned MSMEs and related data
- **Partner**: Read access to partner MSMEs and related data  
- **Agent**: Limited access based on specific assignments

### Security Rules

Firestore security rules are defined in `firestore/rules/firestore.rules` but commented out for development. To enable in production:

1. Uncomment the rules in `firestore.rules`
2. Deploy rules to Firebase: `firebase deploy --only firestore:rules`
3. Update middleware to extract role headers: `X-User-Role`

## 📈 Data Schema

### MSME Schema
```javascript
{
  msme_id: "uuid",
  name: "Business Name",
  gstin: "15-digit alphanumeric",
  chakra_score: 300-900,
  chakra_band: "A|B|C|D|E",
  rm_id: "uuid",
  partner_id: "uuid",
  status: "active|inactive|suspended|pending",
  // ... additional fields
}
```

### Raw Event Schema
```javascript
{
  event_id: "uuid",
  msme_id: "uuid", 
  event_type: "EMI_BOUNCE|GST_DELAY|CASHFLOW_DIP|...",
  event_data: { /* type-specific data */ },
  source: "bank|gst_portal|credit_bureau|internal|partner",
  status: "unprocessed|processing|processed|failed",
  severity: "high|medium|low",
  // ... timestamps
}
```

## 🧪 Testing

### Connection Testing
```bash
# Test Firebase connection
npm run test:connection
node firestore/init.js
```

### Data Validation Testing
```bash
# Validate seeded data
npm run validate:schema

# Test event ingestion
npm run ingest:event
```

### Integration Testing
```bash
# Complete workflow test
npm run seed:all
npm run validate:schema
npm run ingest:event
```

## 🔍 Monitoring & Logging

### Log Levels
- **Error**: Critical failures and exceptions
- **Warn**: Validation failures and data inconsistencies  
- **Info**: Operation progress and success messages
- **Debug**: Detailed execution information

### Log Files
- `logs/error.log`: Error-level logs only
- `logs/combined.log`: All log levels
- Console output: Formatted for development

### Monitoring Commands
```bash
# View recent logs
tail -f logs/combined.log

# Monitor error logs
tail -f logs/error.log

# Check collection statistics
node scripts/cleanup-collections.js --stats
```

## 🚨 Troubleshooting

### Common Issues

1. **Firebase Connection Failed**
   ```bash
   # Check service account key path
   ls -la ../serviceAccountKey.json
   
   # Verify environment variables
   cat .env
   
   # Test connection
   npm run test:connection
   ```

2. **Validation Errors**
   ```bash
   # Run schema validation
   npm run validate:schema
   
   # Check specific collection
   node scripts/validate-schema.js
   ```

3. **Seeding Failures**
   ```bash
   # Check logs for details
   tail -f logs/error.log
   
   # Cleanup and retry
   npm run cleanup -- --confirm
   npm run seed:all
   ```

### Performance Optimization

- **Batch Operations**: All writes use Firestore batch operations (500 docs/batch)
- **Connection Pooling**: Single Firebase Admin SDK instance
- **Validation Caching**: Schema validation results cached per operation
- **Logging Optimization**: Structured logging with appropriate levels

## 🔄 Integration with FastAPI Backend

This Node.js utilities package complements the existing FastAPI backend:

1. **Shared Firebase Project**: Uses same Firestore database and collections
2. **Compatible Schemas**: Data structures match FastAPI models
3. **Service Account**: Shares `serviceAccountKey.json` configuration
4. **Collection Names**: Uses same collection naming conventions
5. **Trigger Engine Support**: Generates events for Python trigger processing

## 📚 API Reference

### Core Classes

- **FirestoreManager**: Firebase Admin SDK initialization and connection management
- **Validators**: Joi schema validation for all data types
- **Seeders**: Individual collection seeding classes
- **EventIngestor**: Event ingestion and trigger testing utilities
- **SchemaValidator**: Data validation and reporting
- **CollectionCleaner**: Safe cleanup and maintenance utilities

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FIREBASE_PROJECT_ID` | Firebase project identifier | `credit-chakra-platform` |
| `FIREBASE_SERVICE_ACCOUNT_KEY_PATH` | Path to service account key | `../serviceAccountKey.json` |
| `COLLECTION_MSMES` | MSME collection name | `msmes` |
| `COLLECTION_RAW_EVENTS` | Raw events collection name | `raw_events` |
| `COLLECTION_ALERTS` | Alerts collection name | `alerts` |
| `COLLECTION_CHAKRA_SCORE` | Chakra score collection name | `chakra_score` |
| `COLLECTION_CONSENT_LEDGER` | Consent ledger collection name | `consent_ledger` |
| `SAMPLE_MSME_COUNT` | Number of sample MSMEs to generate | `5` |
| `SAMPLE_EVENTS_PER_MSME` | Events per MSME for testing | `10` |

## 🤝 Contributing

1. Follow existing code patterns and naming conventions
2. Add comprehensive logging for all operations
3. Include Joi validation for new data types
4. Update documentation for new features
5. Test integration with FastAPI backend
6. Maintain compatibility with existing trigger engine

## 📄 License

ISC License - Credit Chakra Team

---

**Note**: This Node.js utilities package is designed to work alongside the existing FastAPI backend. Role-based access control is currently commented out for development purposes and should be enabled for production deployment.
