(e=>{"function"==typeof define&&define.amd?define(["protobufjs/minimal"],e):"function"==typeof require&&"object"==typeof module&&module&&module.exports&&(module.exports=e(require("protobufjs/minimal")))})(function(r){var e,t,o,n,i,L,a=r.util,s=r.roots.firestore_admin_v1||(r.roots.firestore_admin_v1={});function l(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function F(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function p(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function U(e){if(this.activeKeyVersion=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function B(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function M(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function u(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function V(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function G(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function J(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Y(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function q(e){if(this.indexes=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function W(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function c(e){if(this.fields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function g(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function z(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function H(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function f(e,t,o){r.rpc.Service.call(this,e,t,o)}function K(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function X(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Z(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Q(e){if(this.databases=[],this.unreachable=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function $(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ee(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function te(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function oe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function re(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ne(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ie(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ae(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function se(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function le(e){if(this.backupSchedules=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function pe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ue(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ce(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ge(e){if(this.indexes=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function fe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function de(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ye(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function me(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function be(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Oe(e){if(this.fields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function he(e){if(this.collectionIds=[],this.namespaceIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Se(e){if(this.collectionIds=[],this.namespaceIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ve(e){if(this.collectionIds=[],this.namespaceIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ee(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Te(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ie(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function je(e){if(this.backups=[],this.unreachable=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ne(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Pe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function d(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function y(e){if(this.indexConfigDeltas=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function De(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function we(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function m(e){if(this.collectionIds=[],this.namespaceIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function b(e){if(this.collectionIds=[],this.namespaceIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function O(e){if(this.collectionIds=[],this.namespaceIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ke(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function h(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Re(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function S(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ce(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function xe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ae(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function v(e){if(this.pattern=[],this.style=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _e(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Le(e){if(this.rules=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function E(e){if(this.additionalBindings=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Fe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ue(e){if(this.destinations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function T(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function I(e){if(this.methodSettings=[],this.codeownerGithubTeams=[],this.librarySettings=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Be(e){if(this.serviceClassNames={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Me(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ve(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ge(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Je(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ye(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function j(e){if(this.renamedServices={},this.renamedResources={},this.ignoredResources=[],this.forcedNamespaceAliases=[],this.handwrittenSignatures=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function qe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function We(e){if(this.renamedServices={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ze(e){if(this.autoPopulatedFields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function He(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ke(e){if(this.methods=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Xe(e){if(this.file=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function N(e){if(this.dependency=[],this.publicDependency=[],this.weakDependency=[],this.messageType=[],this.enumType=[],this.service=[],this.extension=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function P(e){if(this.field=[],this.extension=[],this.nestedType=[],this.enumType=[],this.extensionRange=[],this.oneofDecl=[],this.reservedRange=[],this.reservedName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ze(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Qe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function $e(e){if(this.uninterpretedOption=[],this.declaration=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function et(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function D(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function tt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ot(e){if(this.value=[],this.reservedRange=[],this.reservedName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function rt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function nt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function it(e){if(this.method=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function at(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function w(e){if(this.uninterpretedOption=[],this[".google.api.resourceDefinition"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function k(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function R(e){if(this.targets=[],this.editionDefaults=[],this.uninterpretedOption=[],this[".google.api.fieldBehavior"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function st(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function lt(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function pt(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ut(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ct(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function C(e){if(this.uninterpretedOption=[],this[".google.api.methodSignature"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function x(e){if(this.name=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function gt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function A(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ft(e){if(this.defaults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function dt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function yt(e){if(this.location=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function mt(e){if(this.path=[],this.span=[],this.leadingDetachedComments=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function bt(e){if(this.annotation=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ot(e){if(this.path=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ht(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function St(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function vt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Et(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Tt(e){if(this.paths=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function It(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function jt(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Nt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Pt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Dt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function wt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function kt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Rt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ct(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function xt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function At(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _t(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Lt(e,t,o){r.rpc.Service.call(this,e,t,o)}function Ft(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ut(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Bt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Mt(e){if(this.operations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Vt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Gt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Jt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Yt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function qt(e){if(this.details=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}return s.google=((L={}).firestore=((n={}).admin=((o={}).v1=((e={}).Backup=(l.prototype.name="",l.prototype.database="",l.prototype.databaseUid="",l.prototype.snapshotTime=null,l.prototype.expireTime=null,l.prototype.stats=null,l.prototype.state=0,l.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Backup)return e;var t=new s.google.firestore.admin.v1.Backup;if(null!=e.name&&(t.name=String(e.name)),null!=e.database&&(t.database=String(e.database)),null!=e.databaseUid&&(t.databaseUid=String(e.databaseUid)),null!=e.snapshotTime){if("object"!=typeof e.snapshotTime)throw TypeError(".google.firestore.admin.v1.Backup.snapshotTime: object expected");t.snapshotTime=s.google.protobuf.Timestamp.fromObject(e.snapshotTime)}if(null!=e.expireTime){if("object"!=typeof e.expireTime)throw TypeError(".google.firestore.admin.v1.Backup.expireTime: object expected");t.expireTime=s.google.protobuf.Timestamp.fromObject(e.expireTime)}if(null!=e.stats){if("object"!=typeof e.stats)throw TypeError(".google.firestore.admin.v1.Backup.stats: object expected");t.stats=s.google.firestore.admin.v1.Backup.Stats.fromObject(e.stats)}switch(e.state){default:"number"==typeof e.state&&(t.state=e.state);break;case"STATE_UNSPECIFIED":case 0:t.state=0;break;case"CREATING":case 1:t.state=1;break;case"READY":case 2:t.state=2;break;case"NOT_AVAILABLE":case 3:t.state=3}return t},l.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.database="",o.snapshotTime=null,o.expireTime=null,o.stats=null,o.databaseUid="",o.state=t.enums===String?"STATE_UNSPECIFIED":0),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),null!=e.snapshotTime&&e.hasOwnProperty("snapshotTime")&&(o.snapshotTime=s.google.protobuf.Timestamp.toObject(e.snapshotTime,t)),null!=e.expireTime&&e.hasOwnProperty("expireTime")&&(o.expireTime=s.google.protobuf.Timestamp.toObject(e.expireTime,t)),null!=e.stats&&e.hasOwnProperty("stats")&&(o.stats=s.google.firestore.admin.v1.Backup.Stats.toObject(e.stats,t)),null!=e.databaseUid&&e.hasOwnProperty("databaseUid")&&(o.databaseUid=e.databaseUid),null!=e.state&&e.hasOwnProperty("state")&&(o.state=t.enums!==String||void 0===s.google.firestore.admin.v1.Backup.State[e.state]?e.state:s.google.firestore.admin.v1.Backup.State[e.state]),o},l.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},l.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Backup"},l.Stats=(F.prototype.sizeBytes=a.Long?a.Long.fromBits(0,0,!1):0,F.prototype.documentCount=a.Long?a.Long.fromBits(0,0,!1):0,F.prototype.indexCount=a.Long?a.Long.fromBits(0,0,!1):0,F.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.Backup.Stats?e:(t=new s.google.firestore.admin.v1.Backup.Stats,null!=e.sizeBytes&&(a.Long?(t.sizeBytes=a.Long.fromValue(e.sizeBytes)).unsigned=!1:"string"==typeof e.sizeBytes?t.sizeBytes=parseInt(e.sizeBytes,10):"number"==typeof e.sizeBytes?t.sizeBytes=e.sizeBytes:"object"==typeof e.sizeBytes&&(t.sizeBytes=new a.LongBits(e.sizeBytes.low>>>0,e.sizeBytes.high>>>0).toNumber())),null!=e.documentCount&&(a.Long?(t.documentCount=a.Long.fromValue(e.documentCount)).unsigned=!1:"string"==typeof e.documentCount?t.documentCount=parseInt(e.documentCount,10):"number"==typeof e.documentCount?t.documentCount=e.documentCount:"object"==typeof e.documentCount&&(t.documentCount=new a.LongBits(e.documentCount.low>>>0,e.documentCount.high>>>0).toNumber())),null!=e.indexCount&&(a.Long?(t.indexCount=a.Long.fromValue(e.indexCount)).unsigned=!1:"string"==typeof e.indexCount?t.indexCount=parseInt(e.indexCount,10):"number"==typeof e.indexCount?t.indexCount=e.indexCount:"object"==typeof e.indexCount&&(t.indexCount=new a.LongBits(e.indexCount.low>>>0,e.indexCount.high>>>0).toNumber())),t)},F.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(a.Long?(o=new a.Long(0,0,!1),r.sizeBytes=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.sizeBytes=t.longs===String?"0":0,a.Long?(o=new a.Long(0,0,!1),r.documentCount=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.documentCount=t.longs===String?"0":0,a.Long?(o=new a.Long(0,0,!1),r.indexCount=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.indexCount=t.longs===String?"0":0),null!=e.sizeBytes&&e.hasOwnProperty("sizeBytes")&&("number"==typeof e.sizeBytes?r.sizeBytes=t.longs===String?String(e.sizeBytes):e.sizeBytes:r.sizeBytes=t.longs===String?a.Long.prototype.toString.call(e.sizeBytes):t.longs===Number?new a.LongBits(e.sizeBytes.low>>>0,e.sizeBytes.high>>>0).toNumber():e.sizeBytes),null!=e.documentCount&&e.hasOwnProperty("documentCount")&&("number"==typeof e.documentCount?r.documentCount=t.longs===String?String(e.documentCount):e.documentCount:r.documentCount=t.longs===String?a.Long.prototype.toString.call(e.documentCount):t.longs===Number?new a.LongBits(e.documentCount.low>>>0,e.documentCount.high>>>0).toNumber():e.documentCount),null!=e.indexCount&&e.hasOwnProperty("indexCount")&&("number"==typeof e.indexCount?r.indexCount=t.longs===String?String(e.indexCount):e.indexCount:r.indexCount=t.longs===String?a.Long.prototype.toString.call(e.indexCount):t.longs===Number?new a.LongBits(e.indexCount.low>>>0,e.indexCount.high>>>0).toNumber():e.indexCount),r},F.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},F.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Backup.Stats"},F),l.State=(i={},(t=Object.create(i))[i[0]="STATE_UNSPECIFIED"]="STATE_UNSPECIFIED",t[i[1]="CREATING"]="CREATING",t[i[2]="READY"]="READY",t[i[3]="NOT_AVAILABLE"]="NOT_AVAILABLE",t),l),e.Database=(p.prototype.name="",p.prototype.uid="",p.prototype.createTime=null,p.prototype.updateTime=null,p.prototype.deleteTime=null,p.prototype.locationId="",p.prototype.type=0,p.prototype.concurrencyMode=0,p.prototype.versionRetentionPeriod=null,p.prototype.earliestVersionTime=null,p.prototype.pointInTimeRecoveryEnablement=0,p.prototype.appEngineIntegrationMode=0,p.prototype.keyPrefix="",p.prototype.deleteProtectionState=0,p.prototype.cmekConfig=null,p.prototype.previousId="",p.prototype.sourceInfo=null,p.prototype.etag="",p.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Database)return e;var t=new s.google.firestore.admin.v1.Database;if(null!=e.name&&(t.name=String(e.name)),null!=e.uid&&(t.uid=String(e.uid)),null!=e.createTime){if("object"!=typeof e.createTime)throw TypeError(".google.firestore.admin.v1.Database.createTime: object expected");t.createTime=s.google.protobuf.Timestamp.fromObject(e.createTime)}if(null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.admin.v1.Database.updateTime: object expected");t.updateTime=s.google.protobuf.Timestamp.fromObject(e.updateTime)}if(null!=e.deleteTime){if("object"!=typeof e.deleteTime)throw TypeError(".google.firestore.admin.v1.Database.deleteTime: object expected");t.deleteTime=s.google.protobuf.Timestamp.fromObject(e.deleteTime)}switch(null!=e.locationId&&(t.locationId=String(e.locationId)),e.type){default:"number"==typeof e.type&&(t.type=e.type);break;case"DATABASE_TYPE_UNSPECIFIED":case 0:t.type=0;break;case"FIRESTORE_NATIVE":case 1:t.type=1;break;case"DATASTORE_MODE":case 2:t.type=2}switch(e.concurrencyMode){default:"number"==typeof e.concurrencyMode&&(t.concurrencyMode=e.concurrencyMode);break;case"CONCURRENCY_MODE_UNSPECIFIED":case 0:t.concurrencyMode=0;break;case"OPTIMISTIC":case 1:t.concurrencyMode=1;break;case"PESSIMISTIC":case 2:t.concurrencyMode=2;break;case"OPTIMISTIC_WITH_ENTITY_GROUPS":case 3:t.concurrencyMode=3}if(null!=e.versionRetentionPeriod){if("object"!=typeof e.versionRetentionPeriod)throw TypeError(".google.firestore.admin.v1.Database.versionRetentionPeriod: object expected");t.versionRetentionPeriod=s.google.protobuf.Duration.fromObject(e.versionRetentionPeriod)}if(null!=e.earliestVersionTime){if("object"!=typeof e.earliestVersionTime)throw TypeError(".google.firestore.admin.v1.Database.earliestVersionTime: object expected");t.earliestVersionTime=s.google.protobuf.Timestamp.fromObject(e.earliestVersionTime)}switch(e.pointInTimeRecoveryEnablement){default:"number"==typeof e.pointInTimeRecoveryEnablement&&(t.pointInTimeRecoveryEnablement=e.pointInTimeRecoveryEnablement);break;case"POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED":case 0:t.pointInTimeRecoveryEnablement=0;break;case"POINT_IN_TIME_RECOVERY_ENABLED":case 1:t.pointInTimeRecoveryEnablement=1;break;case"POINT_IN_TIME_RECOVERY_DISABLED":case 2:t.pointInTimeRecoveryEnablement=2}switch(e.appEngineIntegrationMode){default:"number"==typeof e.appEngineIntegrationMode&&(t.appEngineIntegrationMode=e.appEngineIntegrationMode);break;case"APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED":case 0:t.appEngineIntegrationMode=0;break;case"ENABLED":case 1:t.appEngineIntegrationMode=1;break;case"DISABLED":case 2:t.appEngineIntegrationMode=2}switch(null!=e.keyPrefix&&(t.keyPrefix=String(e.keyPrefix)),e.deleteProtectionState){default:"number"==typeof e.deleteProtectionState&&(t.deleteProtectionState=e.deleteProtectionState);break;case"DELETE_PROTECTION_STATE_UNSPECIFIED":case 0:t.deleteProtectionState=0;break;case"DELETE_PROTECTION_DISABLED":case 1:t.deleteProtectionState=1;break;case"DELETE_PROTECTION_ENABLED":case 2:t.deleteProtectionState=2}if(null!=e.cmekConfig){if("object"!=typeof e.cmekConfig)throw TypeError(".google.firestore.admin.v1.Database.cmekConfig: object expected");t.cmekConfig=s.google.firestore.admin.v1.Database.CmekConfig.fromObject(e.cmekConfig)}if(null!=e.previousId&&(t.previousId=String(e.previousId)),null!=e.sourceInfo){if("object"!=typeof e.sourceInfo)throw TypeError(".google.firestore.admin.v1.Database.sourceInfo: object expected");t.sourceInfo=s.google.firestore.admin.v1.Database.SourceInfo.fromObject(e.sourceInfo)}return null!=e.etag&&(t.etag=String(e.etag)),t},p.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.uid="",o.createTime=null,o.updateTime=null,o.deleteTime=null,o.locationId="",o.type=t.enums===String?"DATABASE_TYPE_UNSPECIFIED":0,o.concurrencyMode=t.enums===String?"CONCURRENCY_MODE_UNSPECIFIED":0,o.versionRetentionPeriod=null,o.earliestVersionTime=null,o.appEngineIntegrationMode=t.enums===String?"APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED":0,o.keyPrefix="",o.pointInTimeRecoveryEnablement=t.enums===String?"POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED":0,o.deleteProtectionState=t.enums===String?"DELETE_PROTECTION_STATE_UNSPECIFIED":0,o.cmekConfig=null,o.previousId="",o.sourceInfo=null,o.etag=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.uid&&e.hasOwnProperty("uid")&&(o.uid=e.uid),null!=e.createTime&&e.hasOwnProperty("createTime")&&(o.createTime=s.google.protobuf.Timestamp.toObject(e.createTime,t)),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(o.updateTime=s.google.protobuf.Timestamp.toObject(e.updateTime,t)),null!=e.deleteTime&&e.hasOwnProperty("deleteTime")&&(o.deleteTime=s.google.protobuf.Timestamp.toObject(e.deleteTime,t)),null!=e.locationId&&e.hasOwnProperty("locationId")&&(o.locationId=e.locationId),null!=e.type&&e.hasOwnProperty("type")&&(o.type=t.enums!==String||void 0===s.google.firestore.admin.v1.Database.DatabaseType[e.type]?e.type:s.google.firestore.admin.v1.Database.DatabaseType[e.type]),null!=e.concurrencyMode&&e.hasOwnProperty("concurrencyMode")&&(o.concurrencyMode=t.enums!==String||void 0===s.google.firestore.admin.v1.Database.ConcurrencyMode[e.concurrencyMode]?e.concurrencyMode:s.google.firestore.admin.v1.Database.ConcurrencyMode[e.concurrencyMode]),null!=e.versionRetentionPeriod&&e.hasOwnProperty("versionRetentionPeriod")&&(o.versionRetentionPeriod=s.google.protobuf.Duration.toObject(e.versionRetentionPeriod,t)),null!=e.earliestVersionTime&&e.hasOwnProperty("earliestVersionTime")&&(o.earliestVersionTime=s.google.protobuf.Timestamp.toObject(e.earliestVersionTime,t)),null!=e.appEngineIntegrationMode&&e.hasOwnProperty("appEngineIntegrationMode")&&(o.appEngineIntegrationMode=t.enums!==String||void 0===s.google.firestore.admin.v1.Database.AppEngineIntegrationMode[e.appEngineIntegrationMode]?e.appEngineIntegrationMode:s.google.firestore.admin.v1.Database.AppEngineIntegrationMode[e.appEngineIntegrationMode]),null!=e.keyPrefix&&e.hasOwnProperty("keyPrefix")&&(o.keyPrefix=e.keyPrefix),null!=e.pointInTimeRecoveryEnablement&&e.hasOwnProperty("pointInTimeRecoveryEnablement")&&(o.pointInTimeRecoveryEnablement=t.enums!==String||void 0===s.google.firestore.admin.v1.Database.PointInTimeRecoveryEnablement[e.pointInTimeRecoveryEnablement]?e.pointInTimeRecoveryEnablement:s.google.firestore.admin.v1.Database.PointInTimeRecoveryEnablement[e.pointInTimeRecoveryEnablement]),null!=e.deleteProtectionState&&e.hasOwnProperty("deleteProtectionState")&&(o.deleteProtectionState=t.enums!==String||void 0===s.google.firestore.admin.v1.Database.DeleteProtectionState[e.deleteProtectionState]?e.deleteProtectionState:s.google.firestore.admin.v1.Database.DeleteProtectionState[e.deleteProtectionState]),null!=e.cmekConfig&&e.hasOwnProperty("cmekConfig")&&(o.cmekConfig=s.google.firestore.admin.v1.Database.CmekConfig.toObject(e.cmekConfig,t)),null!=e.previousId&&e.hasOwnProperty("previousId")&&(o.previousId=e.previousId),null!=e.sourceInfo&&e.hasOwnProperty("sourceInfo")&&(o.sourceInfo=s.google.firestore.admin.v1.Database.SourceInfo.toObject(e.sourceInfo,t)),null!=e.etag&&e.hasOwnProperty("etag")&&(o.etag=e.etag),o},p.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},p.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database"},p.DatabaseType=(i={},(t=Object.create(i))[i[0]="DATABASE_TYPE_UNSPECIFIED"]="DATABASE_TYPE_UNSPECIFIED",t[i[1]="FIRESTORE_NATIVE"]="FIRESTORE_NATIVE",t[i[2]="DATASTORE_MODE"]="DATASTORE_MODE",t),p.ConcurrencyMode=(i={},(t=Object.create(i))[i[0]="CONCURRENCY_MODE_UNSPECIFIED"]="CONCURRENCY_MODE_UNSPECIFIED",t[i[1]="OPTIMISTIC"]="OPTIMISTIC",t[i[2]="PESSIMISTIC"]="PESSIMISTIC",t[i[3]="OPTIMISTIC_WITH_ENTITY_GROUPS"]="OPTIMISTIC_WITH_ENTITY_GROUPS",t),p.PointInTimeRecoveryEnablement=(i={},(t=Object.create(i))[i[0]="POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED"]="POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED",t[i[1]="POINT_IN_TIME_RECOVERY_ENABLED"]="POINT_IN_TIME_RECOVERY_ENABLED",t[i[2]="POINT_IN_TIME_RECOVERY_DISABLED"]="POINT_IN_TIME_RECOVERY_DISABLED",t),p.AppEngineIntegrationMode=(i={},(t=Object.create(i))[i[0]="APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED"]="APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED",t[i[1]="ENABLED"]="ENABLED",t[i[2]="DISABLED"]="DISABLED",t),p.DeleteProtectionState=(i={},(t=Object.create(i))[i[0]="DELETE_PROTECTION_STATE_UNSPECIFIED"]="DELETE_PROTECTION_STATE_UNSPECIFIED",t[i[1]="DELETE_PROTECTION_DISABLED"]="DELETE_PROTECTION_DISABLED",t[i[2]="DELETE_PROTECTION_ENABLED"]="DELETE_PROTECTION_ENABLED",t),p.CmekConfig=(U.prototype.kmsKeyName="",U.prototype.activeKeyVersion=a.emptyArray,U.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Database.CmekConfig)return e;var t=new s.google.firestore.admin.v1.Database.CmekConfig;if(null!=e.kmsKeyName&&(t.kmsKeyName=String(e.kmsKeyName)),e.activeKeyVersion){if(!Array.isArray(e.activeKeyVersion))throw TypeError(".google.firestore.admin.v1.Database.CmekConfig.activeKeyVersion: array expected");t.activeKeyVersion=[];for(var o=0;o<e.activeKeyVersion.length;++o)t.activeKeyVersion[o]=String(e.activeKeyVersion[o])}return t},U.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.activeKeyVersion=[]),t.defaults&&(o.kmsKeyName=""),null!=e.kmsKeyName&&e.hasOwnProperty("kmsKeyName")&&(o.kmsKeyName=e.kmsKeyName),e.activeKeyVersion&&e.activeKeyVersion.length){o.activeKeyVersion=[];for(var r=0;r<e.activeKeyVersion.length;++r)o.activeKeyVersion[r]=e.activeKeyVersion[r]}return o},U.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},U.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.CmekConfig"},U),p.SourceInfo=(B.prototype.backup=null,B.prototype.operation="",Object.defineProperty(B.prototype,"source",{get:a.oneOfGetter(i=["backup"]),set:a.oneOfSetter(i)}),B.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Database.SourceInfo)return e;var t=new s.google.firestore.admin.v1.Database.SourceInfo;if(null!=e.backup){if("object"!=typeof e.backup)throw TypeError(".google.firestore.admin.v1.Database.SourceInfo.backup: object expected");t.backup=s.google.firestore.admin.v1.Database.SourceInfo.BackupSource.fromObject(e.backup)}return null!=e.operation&&(t.operation=String(e.operation)),t},B.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.operation=""),null!=e.backup&&e.hasOwnProperty("backup")&&(o.backup=s.google.firestore.admin.v1.Database.SourceInfo.BackupSource.toObject(e.backup,t),t.oneofs)&&(o.source="backup"),null!=e.operation&&e.hasOwnProperty("operation")&&(o.operation=e.operation),o},B.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},B.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.SourceInfo"},B.BackupSource=(M.prototype.backup="",M.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.Database.SourceInfo.BackupSource?e:(t=new s.google.firestore.admin.v1.Database.SourceInfo.BackupSource,null!=e.backup&&(t.backup=String(e.backup)),t)},M.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.backup=""),null!=e.backup&&e.hasOwnProperty("backup")&&(o.backup=e.backup),o},M.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},M.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.SourceInfo.BackupSource"},M),B),p.EncryptionConfig=(u.prototype.googleDefaultEncryption=null,u.prototype.useSourceEncryption=null,u.prototype.customerManagedEncryption=null,Object.defineProperty(u.prototype,"encryptionType",{get:a.oneOfGetter(t=["googleDefaultEncryption","useSourceEncryption","customerManagedEncryption"]),set:a.oneOfSetter(t)}),u.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Database.EncryptionConfig)return e;var t=new s.google.firestore.admin.v1.Database.EncryptionConfig;if(null!=e.googleDefaultEncryption){if("object"!=typeof e.googleDefaultEncryption)throw TypeError(".google.firestore.admin.v1.Database.EncryptionConfig.googleDefaultEncryption: object expected");t.googleDefaultEncryption=s.google.firestore.admin.v1.Database.EncryptionConfig.GoogleDefaultEncryptionOptions.fromObject(e.googleDefaultEncryption)}if(null!=e.useSourceEncryption){if("object"!=typeof e.useSourceEncryption)throw TypeError(".google.firestore.admin.v1.Database.EncryptionConfig.useSourceEncryption: object expected");t.useSourceEncryption=s.google.firestore.admin.v1.Database.EncryptionConfig.SourceEncryptionOptions.fromObject(e.useSourceEncryption)}if(null!=e.customerManagedEncryption){if("object"!=typeof e.customerManagedEncryption)throw TypeError(".google.firestore.admin.v1.Database.EncryptionConfig.customerManagedEncryption: object expected");t.customerManagedEncryption=s.google.firestore.admin.v1.Database.EncryptionConfig.CustomerManagedEncryptionOptions.fromObject(e.customerManagedEncryption)}return t},u.toObject=function(e,t){t=t||{};var o={};return null!=e.googleDefaultEncryption&&e.hasOwnProperty("googleDefaultEncryption")&&(o.googleDefaultEncryption=s.google.firestore.admin.v1.Database.EncryptionConfig.GoogleDefaultEncryptionOptions.toObject(e.googleDefaultEncryption,t),t.oneofs)&&(o.encryptionType="googleDefaultEncryption"),null!=e.useSourceEncryption&&e.hasOwnProperty("useSourceEncryption")&&(o.useSourceEncryption=s.google.firestore.admin.v1.Database.EncryptionConfig.SourceEncryptionOptions.toObject(e.useSourceEncryption,t),t.oneofs)&&(o.encryptionType="useSourceEncryption"),null!=e.customerManagedEncryption&&e.hasOwnProperty("customerManagedEncryption")&&(o.customerManagedEncryption=s.google.firestore.admin.v1.Database.EncryptionConfig.CustomerManagedEncryptionOptions.toObject(e.customerManagedEncryption,t),t.oneofs)&&(o.encryptionType="customerManagedEncryption"),o},u.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},u.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.EncryptionConfig"},u.GoogleDefaultEncryptionOptions=(V.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.Database.EncryptionConfig.GoogleDefaultEncryptionOptions?e:new s.google.firestore.admin.v1.Database.EncryptionConfig.GoogleDefaultEncryptionOptions},V.toObject=function(){return{}},V.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},V.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.EncryptionConfig.GoogleDefaultEncryptionOptions"},V),u.SourceEncryptionOptions=(G.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.Database.EncryptionConfig.SourceEncryptionOptions?e:new s.google.firestore.admin.v1.Database.EncryptionConfig.SourceEncryptionOptions},G.toObject=function(){return{}},G.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},G.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.EncryptionConfig.SourceEncryptionOptions"},G),u.CustomerManagedEncryptionOptions=(J.prototype.kmsKeyName="",J.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.Database.EncryptionConfig.CustomerManagedEncryptionOptions?e:(t=new s.google.firestore.admin.v1.Database.EncryptionConfig.CustomerManagedEncryptionOptions,null!=e.kmsKeyName&&(t.kmsKeyName=String(e.kmsKeyName)),t)},J.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.kmsKeyName=""),null!=e.kmsKeyName&&e.hasOwnProperty("kmsKeyName")&&(o.kmsKeyName=e.kmsKeyName),o},J.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},J.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Database.EncryptionConfig.CustomerManagedEncryptionOptions"},J),u),p),e.Field=(Y.prototype.name="",Y.prototype.indexConfig=null,Y.prototype.ttlConfig=null,Y.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Field)return e;var t=new s.google.firestore.admin.v1.Field;if(null!=e.name&&(t.name=String(e.name)),null!=e.indexConfig){if("object"!=typeof e.indexConfig)throw TypeError(".google.firestore.admin.v1.Field.indexConfig: object expected");t.indexConfig=s.google.firestore.admin.v1.Field.IndexConfig.fromObject(e.indexConfig)}if(null!=e.ttlConfig){if("object"!=typeof e.ttlConfig)throw TypeError(".google.firestore.admin.v1.Field.ttlConfig: object expected");t.ttlConfig=s.google.firestore.admin.v1.Field.TtlConfig.fromObject(e.ttlConfig)}return t},Y.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.indexConfig=null,o.ttlConfig=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.indexConfig&&e.hasOwnProperty("indexConfig")&&(o.indexConfig=s.google.firestore.admin.v1.Field.IndexConfig.toObject(e.indexConfig,t)),null!=e.ttlConfig&&e.hasOwnProperty("ttlConfig")&&(o.ttlConfig=s.google.firestore.admin.v1.Field.TtlConfig.toObject(e.ttlConfig,t)),o},Y.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Y.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Field"},Y.IndexConfig=(q.prototype.indexes=a.emptyArray,q.prototype.usesAncestorConfig=!1,q.prototype.ancestorField="",q.prototype.reverting=!1,q.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Field.IndexConfig)return e;var t=new s.google.firestore.admin.v1.Field.IndexConfig;if(e.indexes){if(!Array.isArray(e.indexes))throw TypeError(".google.firestore.admin.v1.Field.IndexConfig.indexes: array expected");t.indexes=[];for(var o=0;o<e.indexes.length;++o){if("object"!=typeof e.indexes[o])throw TypeError(".google.firestore.admin.v1.Field.IndexConfig.indexes: object expected");t.indexes[o]=s.google.firestore.admin.v1.Index.fromObject(e.indexes[o])}}return null!=e.usesAncestorConfig&&(t.usesAncestorConfig=Boolean(e.usesAncestorConfig)),null!=e.ancestorField&&(t.ancestorField=String(e.ancestorField)),null!=e.reverting&&(t.reverting=Boolean(e.reverting)),t},q.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.indexes=[]),t.defaults&&(o.usesAncestorConfig=!1,o.ancestorField="",o.reverting=!1),e.indexes&&e.indexes.length){o.indexes=[];for(var r=0;r<e.indexes.length;++r)o.indexes[r]=s.google.firestore.admin.v1.Index.toObject(e.indexes[r],t)}return null!=e.usesAncestorConfig&&e.hasOwnProperty("usesAncestorConfig")&&(o.usesAncestorConfig=e.usesAncestorConfig),null!=e.ancestorField&&e.hasOwnProperty("ancestorField")&&(o.ancestorField=e.ancestorField),null!=e.reverting&&e.hasOwnProperty("reverting")&&(o.reverting=e.reverting),o},q.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},q.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Field.IndexConfig"},q),Y.TtlConfig=(W.prototype.state=0,W.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Field.TtlConfig)return e;var t=new s.google.firestore.admin.v1.Field.TtlConfig;switch(e.state){default:"number"==typeof e.state&&(t.state=e.state);break;case"STATE_UNSPECIFIED":case 0:t.state=0;break;case"CREATING":case 1:t.state=1;break;case"ACTIVE":case 2:t.state=2;break;case"NEEDS_REPAIR":case 3:t.state=3}return t},W.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.state=t.enums===String?"STATE_UNSPECIFIED":0),null!=e.state&&e.hasOwnProperty("state")&&(o.state=t.enums!==String||void 0===s.google.firestore.admin.v1.Field.TtlConfig.State[e.state]?e.state:s.google.firestore.admin.v1.Field.TtlConfig.State[e.state]),o},W.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},W.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Field.TtlConfig"},W.State=(i={},(t=Object.create(i))[i[0]="STATE_UNSPECIFIED"]="STATE_UNSPECIFIED",t[i[1]="CREATING"]="CREATING",t[i[2]="ACTIVE"]="ACTIVE",t[i[3]="NEEDS_REPAIR"]="NEEDS_REPAIR",t),W),Y),e.Index=(c.prototype.name="",c.prototype.queryScope=0,c.prototype.apiScope=0,c.prototype.fields=a.emptyArray,c.prototype.state=0,c.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Index)return e;var t=new s.google.firestore.admin.v1.Index;switch(null!=e.name&&(t.name=String(e.name)),e.queryScope){default:"number"==typeof e.queryScope&&(t.queryScope=e.queryScope);break;case"QUERY_SCOPE_UNSPECIFIED":case 0:t.queryScope=0;break;case"COLLECTION":case 1:t.queryScope=1;break;case"COLLECTION_GROUP":case 2:t.queryScope=2;break;case"COLLECTION_RECURSIVE":case 3:t.queryScope=3}switch(e.apiScope){default:"number"==typeof e.apiScope&&(t.apiScope=e.apiScope);break;case"ANY_API":case 0:t.apiScope=0;break;case"DATASTORE_MODE_API":case 1:t.apiScope=1}if(e.fields){if(!Array.isArray(e.fields))throw TypeError(".google.firestore.admin.v1.Index.fields: array expected");t.fields=[];for(var o=0;o<e.fields.length;++o){if("object"!=typeof e.fields[o])throw TypeError(".google.firestore.admin.v1.Index.fields: object expected");t.fields[o]=s.google.firestore.admin.v1.Index.IndexField.fromObject(e.fields[o])}}switch(e.state){default:"number"==typeof e.state&&(t.state=e.state);break;case"STATE_UNSPECIFIED":case 0:t.state=0;break;case"CREATING":case 1:t.state=1;break;case"READY":case 2:t.state=2;break;case"NEEDS_REPAIR":case 3:t.state=3}return t},c.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fields=[]),t.defaults&&(o.name="",o.queryScope=t.enums===String?"QUERY_SCOPE_UNSPECIFIED":0,o.state=t.enums===String?"STATE_UNSPECIFIED":0,o.apiScope=t.enums===String?"ANY_API":0),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.queryScope&&e.hasOwnProperty("queryScope")&&(o.queryScope=t.enums!==String||void 0===s.google.firestore.admin.v1.Index.QueryScope[e.queryScope]?e.queryScope:s.google.firestore.admin.v1.Index.QueryScope[e.queryScope]),e.fields&&e.fields.length){o.fields=[];for(var r=0;r<e.fields.length;++r)o.fields[r]=s.google.firestore.admin.v1.Index.IndexField.toObject(e.fields[r],t)}return null!=e.state&&e.hasOwnProperty("state")&&(o.state=t.enums!==String||void 0===s.google.firestore.admin.v1.Index.State[e.state]?e.state:s.google.firestore.admin.v1.Index.State[e.state]),null!=e.apiScope&&e.hasOwnProperty("apiScope")&&(o.apiScope=t.enums!==String||void 0===s.google.firestore.admin.v1.Index.ApiScope[e.apiScope]?e.apiScope:s.google.firestore.admin.v1.Index.ApiScope[e.apiScope]),o},c.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},c.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Index"},c.QueryScope=(i={},(t=Object.create(i))[i[0]="QUERY_SCOPE_UNSPECIFIED"]="QUERY_SCOPE_UNSPECIFIED",t[i[1]="COLLECTION"]="COLLECTION",t[i[2]="COLLECTION_GROUP"]="COLLECTION_GROUP",t[i[3]="COLLECTION_RECURSIVE"]="COLLECTION_RECURSIVE",t),c.ApiScope=(i={},(t=Object.create(i))[i[0]="ANY_API"]="ANY_API",t[i[1]="DATASTORE_MODE_API"]="DATASTORE_MODE_API",t),c.IndexField=(g.prototype.fieldPath="",g.prototype.order=null,g.prototype.arrayConfig=null,g.prototype.vectorConfig=null,Object.defineProperty(g.prototype,"valueMode",{get:a.oneOfGetter(i=["order","arrayConfig","vectorConfig"]),set:a.oneOfSetter(i)}),g.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Index.IndexField)return e;var t=new s.google.firestore.admin.v1.Index.IndexField;switch(null!=e.fieldPath&&(t.fieldPath=String(e.fieldPath)),e.order){default:"number"==typeof e.order&&(t.order=e.order);break;case"ORDER_UNSPECIFIED":case 0:t.order=0;break;case"ASCENDING":case 1:t.order=1;break;case"DESCENDING":case 2:t.order=2}switch(e.arrayConfig){default:"number"==typeof e.arrayConfig&&(t.arrayConfig=e.arrayConfig);break;case"ARRAY_CONFIG_UNSPECIFIED":case 0:t.arrayConfig=0;break;case"CONTAINS":case 1:t.arrayConfig=1}if(null!=e.vectorConfig){if("object"!=typeof e.vectorConfig)throw TypeError(".google.firestore.admin.v1.Index.IndexField.vectorConfig: object expected");t.vectorConfig=s.google.firestore.admin.v1.Index.IndexField.VectorConfig.fromObject(e.vectorConfig)}return t},g.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPath=""),null!=e.fieldPath&&e.hasOwnProperty("fieldPath")&&(o.fieldPath=e.fieldPath),null!=e.order&&e.hasOwnProperty("order")&&(o.order=t.enums!==String||void 0===s.google.firestore.admin.v1.Index.IndexField.Order[e.order]?e.order:s.google.firestore.admin.v1.Index.IndexField.Order[e.order],t.oneofs)&&(o.valueMode="order"),null!=e.arrayConfig&&e.hasOwnProperty("arrayConfig")&&(o.arrayConfig=t.enums!==String||void 0===s.google.firestore.admin.v1.Index.IndexField.ArrayConfig[e.arrayConfig]?e.arrayConfig:s.google.firestore.admin.v1.Index.IndexField.ArrayConfig[e.arrayConfig],t.oneofs)&&(o.valueMode="arrayConfig"),null!=e.vectorConfig&&e.hasOwnProperty("vectorConfig")&&(o.vectorConfig=s.google.firestore.admin.v1.Index.IndexField.VectorConfig.toObject(e.vectorConfig,t),t.oneofs)&&(o.valueMode="vectorConfig"),o},g.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},g.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Index.IndexField"},g.Order=(i={},(t=Object.create(i))[i[0]="ORDER_UNSPECIFIED"]="ORDER_UNSPECIFIED",t[i[1]="ASCENDING"]="ASCENDING",t[i[2]="DESCENDING"]="DESCENDING",t),g.ArrayConfig=(i={},(t=Object.create(i))[i[0]="ARRAY_CONFIG_UNSPECIFIED"]="ARRAY_CONFIG_UNSPECIFIED",t[i[1]="CONTAINS"]="CONTAINS",t),g.VectorConfig=(z.prototype.dimension=0,z.prototype.flat=null,Object.defineProperty(z.prototype,"type",{get:a.oneOfGetter(i=["flat"]),set:a.oneOfSetter(i)}),z.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.Index.IndexField.VectorConfig)return e;var t=new s.google.firestore.admin.v1.Index.IndexField.VectorConfig;if(null!=e.dimension&&(t.dimension=0|e.dimension),null!=e.flat){if("object"!=typeof e.flat)throw TypeError(".google.firestore.admin.v1.Index.IndexField.VectorConfig.flat: object expected");t.flat=s.google.firestore.admin.v1.Index.IndexField.VectorConfig.FlatIndex.fromObject(e.flat)}return t},z.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.dimension=0),null!=e.dimension&&e.hasOwnProperty("dimension")&&(o.dimension=e.dimension),null!=e.flat&&e.hasOwnProperty("flat")&&(o.flat=s.google.firestore.admin.v1.Index.IndexField.VectorConfig.FlatIndex.toObject(e.flat,t),t.oneofs)&&(o.type="flat"),o},z.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},z.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Index.IndexField.VectorConfig"},z.FlatIndex=(H.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.Index.IndexField.VectorConfig.FlatIndex?e:new s.google.firestore.admin.v1.Index.IndexField.VectorConfig.FlatIndex},H.toObject=function(){return{}},H.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},H.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Index.IndexField.VectorConfig.FlatIndex"},H),z),g),c.State=(t={},(i=Object.create(t))[t[0]="STATE_UNSPECIFIED"]="STATE_UNSPECIFIED",i[t[1]="CREATING"]="CREATING",i[t[2]="READY"]="READY",i[t[3]="NEEDS_REPAIR"]="NEEDS_REPAIR",i),c),e.FirestoreAdmin=((f.prototype=Object.create(r.rpc.Service.prototype)).constructor=f,Object.defineProperty(f.prototype.createIndex=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.CreateIndexRequest,s.google.longrunning.Operation,t,o)},"name",{value:"CreateIndex"}),Object.defineProperty(f.prototype.listIndexes=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ListIndexesRequest,s.google.firestore.admin.v1.ListIndexesResponse,t,o)},"name",{value:"ListIndexes"}),Object.defineProperty(f.prototype.getIndex=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.GetIndexRequest,s.google.firestore.admin.v1.Index,t,o)},"name",{value:"GetIndex"}),Object.defineProperty(f.prototype.deleteIndex=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.DeleteIndexRequest,s.google.protobuf.Empty,t,o)},"name",{value:"DeleteIndex"}),Object.defineProperty(f.prototype.getField=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.GetFieldRequest,s.google.firestore.admin.v1.Field,t,o)},"name",{value:"GetField"}),Object.defineProperty(f.prototype.updateField=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.UpdateFieldRequest,s.google.longrunning.Operation,t,o)},"name",{value:"UpdateField"}),Object.defineProperty(f.prototype.listFields=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ListFieldsRequest,s.google.firestore.admin.v1.ListFieldsResponse,t,o)},"name",{value:"ListFields"}),Object.defineProperty(f.prototype.exportDocuments=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ExportDocumentsRequest,s.google.longrunning.Operation,t,o)},"name",{value:"ExportDocuments"}),Object.defineProperty(f.prototype.importDocuments=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ImportDocumentsRequest,s.google.longrunning.Operation,t,o)},"name",{value:"ImportDocuments"}),Object.defineProperty(f.prototype.bulkDeleteDocuments=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.BulkDeleteDocumentsRequest,s.google.longrunning.Operation,t,o)},"name",{value:"BulkDeleteDocuments"}),Object.defineProperty(f.prototype.createDatabase=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.CreateDatabaseRequest,s.google.longrunning.Operation,t,o)},"name",{value:"CreateDatabase"}),Object.defineProperty(f.prototype.getDatabase=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.GetDatabaseRequest,s.google.firestore.admin.v1.Database,t,o)},"name",{value:"GetDatabase"}),Object.defineProperty(f.prototype.listDatabases=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ListDatabasesRequest,s.google.firestore.admin.v1.ListDatabasesResponse,t,o)},"name",{value:"ListDatabases"}),Object.defineProperty(f.prototype.updateDatabase=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.UpdateDatabaseRequest,s.google.longrunning.Operation,t,o)},"name",{value:"UpdateDatabase"}),Object.defineProperty(f.prototype.deleteDatabase=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.DeleteDatabaseRequest,s.google.longrunning.Operation,t,o)},"name",{value:"DeleteDatabase"}),Object.defineProperty(f.prototype.getBackup=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.GetBackupRequest,s.google.firestore.admin.v1.Backup,t,o)},"name",{value:"GetBackup"}),Object.defineProperty(f.prototype.listBackups=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ListBackupsRequest,s.google.firestore.admin.v1.ListBackupsResponse,t,o)},"name",{value:"ListBackups"}),Object.defineProperty(f.prototype.deleteBackup=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.DeleteBackupRequest,s.google.protobuf.Empty,t,o)},"name",{value:"DeleteBackup"}),Object.defineProperty(f.prototype.restoreDatabase=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.RestoreDatabaseRequest,s.google.longrunning.Operation,t,o)},"name",{value:"RestoreDatabase"}),Object.defineProperty(f.prototype.createBackupSchedule=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.CreateBackupScheduleRequest,s.google.firestore.admin.v1.BackupSchedule,t,o)},"name",{value:"CreateBackupSchedule"}),Object.defineProperty(f.prototype.getBackupSchedule=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.GetBackupScheduleRequest,s.google.firestore.admin.v1.BackupSchedule,t,o)},"name",{value:"GetBackupSchedule"}),Object.defineProperty(f.prototype.listBackupSchedules=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.ListBackupSchedulesRequest,s.google.firestore.admin.v1.ListBackupSchedulesResponse,t,o)},"name",{value:"ListBackupSchedules"}),Object.defineProperty(f.prototype.updateBackupSchedule=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.UpdateBackupScheduleRequest,s.google.firestore.admin.v1.BackupSchedule,t,o)},"name",{value:"UpdateBackupSchedule"}),Object.defineProperty(f.prototype.deleteBackupSchedule=function e(t,o){return this.rpcCall(e,s.google.firestore.admin.v1.DeleteBackupScheduleRequest,s.google.protobuf.Empty,t,o)},"name",{value:"DeleteBackupSchedule"}),f),e.ListDatabasesRequest=(K.prototype.parent="",K.prototype.showDeleted=!1,K.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.ListDatabasesRequest?e:(t=new s.google.firestore.admin.v1.ListDatabasesRequest,null!=e.parent&&(t.parent=String(e.parent)),null!=e.showDeleted&&(t.showDeleted=Boolean(e.showDeleted)),t)},K.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.showDeleted=!1),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.showDeleted&&e.hasOwnProperty("showDeleted")&&(o.showDeleted=e.showDeleted),o},K.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},K.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListDatabasesRequest"},K),e.CreateDatabaseRequest=(X.prototype.parent="",X.prototype.database=null,X.prototype.databaseId="",X.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.CreateDatabaseRequest)return e;var t=new s.google.firestore.admin.v1.CreateDatabaseRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.database){if("object"!=typeof e.database)throw TypeError(".google.firestore.admin.v1.CreateDatabaseRequest.database: object expected");t.database=s.google.firestore.admin.v1.Database.fromObject(e.database)}return null!=e.databaseId&&(t.databaseId=String(e.databaseId)),t},X.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.database=null,o.databaseId=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.database&&e.hasOwnProperty("database")&&(o.database=s.google.firestore.admin.v1.Database.toObject(e.database,t)),null!=e.databaseId&&e.hasOwnProperty("databaseId")&&(o.databaseId=e.databaseId),o},X.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},X.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.CreateDatabaseRequest"},X),e.CreateDatabaseMetadata=(Z.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.CreateDatabaseMetadata?e:new s.google.firestore.admin.v1.CreateDatabaseMetadata},Z.toObject=function(){return{}},Z.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Z.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.CreateDatabaseMetadata"},Z),e.ListDatabasesResponse=(Q.prototype.databases=a.emptyArray,Q.prototype.unreachable=a.emptyArray,Q.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ListDatabasesResponse)return e;var t=new s.google.firestore.admin.v1.ListDatabasesResponse;if(e.databases){if(!Array.isArray(e.databases))throw TypeError(".google.firestore.admin.v1.ListDatabasesResponse.databases: array expected");t.databases=[];for(var o=0;o<e.databases.length;++o){if("object"!=typeof e.databases[o])throw TypeError(".google.firestore.admin.v1.ListDatabasesResponse.databases: object expected");t.databases[o]=s.google.firestore.admin.v1.Database.fromObject(e.databases[o])}}if(e.unreachable){if(!Array.isArray(e.unreachable))throw TypeError(".google.firestore.admin.v1.ListDatabasesResponse.unreachable: array expected");t.unreachable=[];for(o=0;o<e.unreachable.length;++o)t.unreachable[o]=String(e.unreachable[o])}return t},Q.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.databases=[],o.unreachable=[]),e.databases&&e.databases.length){o.databases=[];for(var r=0;r<e.databases.length;++r)o.databases[r]=s.google.firestore.admin.v1.Database.toObject(e.databases[r],t)}if(e.unreachable&&e.unreachable.length){o.unreachable=[];for(r=0;r<e.unreachable.length;++r)o.unreachable[r]=e.unreachable[r]}return o},Q.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Q.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListDatabasesResponse"},Q),e.GetDatabaseRequest=($.prototype.name="",$.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.GetDatabaseRequest?e:(t=new s.google.firestore.admin.v1.GetDatabaseRequest,null!=e.name&&(t.name=String(e.name)),t)},$.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},$.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.GetDatabaseRequest"},$),e.UpdateDatabaseRequest=(ee.prototype.database=null,ee.prototype.updateMask=null,ee.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.UpdateDatabaseRequest)return e;var t=new s.google.firestore.admin.v1.UpdateDatabaseRequest;if(null!=e.database){if("object"!=typeof e.database)throw TypeError(".google.firestore.admin.v1.UpdateDatabaseRequest.database: object expected");t.database=s.google.firestore.admin.v1.Database.fromObject(e.database)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.admin.v1.UpdateDatabaseRequest.updateMask: object expected");t.updateMask=s.google.protobuf.FieldMask.fromObject(e.updateMask)}return t},ee.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.database=null,o.updateMask=null),null!=e.database&&e.hasOwnProperty("database")&&(o.database=s.google.firestore.admin.v1.Database.toObject(e.database,t)),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=s.google.protobuf.FieldMask.toObject(e.updateMask,t)),o},ee.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ee.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.UpdateDatabaseRequest"},ee),e.UpdateDatabaseMetadata=(te.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.UpdateDatabaseMetadata?e:new s.google.firestore.admin.v1.UpdateDatabaseMetadata},te.toObject=function(){return{}},te.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},te.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.UpdateDatabaseMetadata"},te),e.DeleteDatabaseRequest=(oe.prototype.name="",oe.prototype.etag="",oe.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.DeleteDatabaseRequest?e:(t=new s.google.firestore.admin.v1.DeleteDatabaseRequest,null!=e.name&&(t.name=String(e.name)),null!=e.etag&&(t.etag=String(e.etag)),t)},oe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.etag=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.etag&&e.hasOwnProperty("etag")&&(o.etag=e.etag),o},oe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},oe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.DeleteDatabaseRequest"},oe),e.DeleteDatabaseMetadata=(re.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.DeleteDatabaseMetadata?e:new s.google.firestore.admin.v1.DeleteDatabaseMetadata},re.toObject=function(){return{}},re.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},re.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.DeleteDatabaseMetadata"},re),e.CreateBackupScheduleRequest=(ne.prototype.parent="",ne.prototype.backupSchedule=null,ne.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.CreateBackupScheduleRequest)return e;var t=new s.google.firestore.admin.v1.CreateBackupScheduleRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.backupSchedule){if("object"!=typeof e.backupSchedule)throw TypeError(".google.firestore.admin.v1.CreateBackupScheduleRequest.backupSchedule: object expected");t.backupSchedule=s.google.firestore.admin.v1.BackupSchedule.fromObject(e.backupSchedule)}return t},ne.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.backupSchedule=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.backupSchedule&&e.hasOwnProperty("backupSchedule")&&(o.backupSchedule=s.google.firestore.admin.v1.BackupSchedule.toObject(e.backupSchedule,t)),o},ne.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ne.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.CreateBackupScheduleRequest"},ne),e.GetBackupScheduleRequest=(ie.prototype.name="",ie.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.GetBackupScheduleRequest?e:(t=new s.google.firestore.admin.v1.GetBackupScheduleRequest,null!=e.name&&(t.name=String(e.name)),t)},ie.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},ie.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ie.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.GetBackupScheduleRequest"},ie),e.UpdateBackupScheduleRequest=(ae.prototype.backupSchedule=null,ae.prototype.updateMask=null,ae.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.UpdateBackupScheduleRequest)return e;var t=new s.google.firestore.admin.v1.UpdateBackupScheduleRequest;if(null!=e.backupSchedule){if("object"!=typeof e.backupSchedule)throw TypeError(".google.firestore.admin.v1.UpdateBackupScheduleRequest.backupSchedule: object expected");t.backupSchedule=s.google.firestore.admin.v1.BackupSchedule.fromObject(e.backupSchedule)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.admin.v1.UpdateBackupScheduleRequest.updateMask: object expected");t.updateMask=s.google.protobuf.FieldMask.fromObject(e.updateMask)}return t},ae.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.backupSchedule=null,o.updateMask=null),null!=e.backupSchedule&&e.hasOwnProperty("backupSchedule")&&(o.backupSchedule=s.google.firestore.admin.v1.BackupSchedule.toObject(e.backupSchedule,t)),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=s.google.protobuf.FieldMask.toObject(e.updateMask,t)),o},ae.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ae.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.UpdateBackupScheduleRequest"},ae),e.ListBackupSchedulesRequest=(se.prototype.parent="",se.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.ListBackupSchedulesRequest?e:(t=new s.google.firestore.admin.v1.ListBackupSchedulesRequest,null!=e.parent&&(t.parent=String(e.parent)),t)},se.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),o},se.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},se.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListBackupSchedulesRequest"},se),e.ListBackupSchedulesResponse=(le.prototype.backupSchedules=a.emptyArray,le.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ListBackupSchedulesResponse)return e;var t=new s.google.firestore.admin.v1.ListBackupSchedulesResponse;if(e.backupSchedules){if(!Array.isArray(e.backupSchedules))throw TypeError(".google.firestore.admin.v1.ListBackupSchedulesResponse.backupSchedules: array expected");t.backupSchedules=[];for(var o=0;o<e.backupSchedules.length;++o){if("object"!=typeof e.backupSchedules[o])throw TypeError(".google.firestore.admin.v1.ListBackupSchedulesResponse.backupSchedules: object expected");t.backupSchedules[o]=s.google.firestore.admin.v1.BackupSchedule.fromObject(e.backupSchedules[o])}}return t},le.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.backupSchedules=[]),e.backupSchedules&&e.backupSchedules.length){o.backupSchedules=[];for(var r=0;r<e.backupSchedules.length;++r)o.backupSchedules[r]=s.google.firestore.admin.v1.BackupSchedule.toObject(e.backupSchedules[r],t)}return o},le.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},le.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListBackupSchedulesResponse"},le),e.DeleteBackupScheduleRequest=(pe.prototype.name="",pe.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.DeleteBackupScheduleRequest?e:(t=new s.google.firestore.admin.v1.DeleteBackupScheduleRequest,null!=e.name&&(t.name=String(e.name)),t)},pe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},pe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},pe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.DeleteBackupScheduleRequest"},pe),e.CreateIndexRequest=(ue.prototype.parent="",ue.prototype.index=null,ue.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.CreateIndexRequest)return e;var t=new s.google.firestore.admin.v1.CreateIndexRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.index){if("object"!=typeof e.index)throw TypeError(".google.firestore.admin.v1.CreateIndexRequest.index: object expected");t.index=s.google.firestore.admin.v1.Index.fromObject(e.index)}return t},ue.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.index=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.index&&e.hasOwnProperty("index")&&(o.index=s.google.firestore.admin.v1.Index.toObject(e.index,t)),o},ue.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ue.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.CreateIndexRequest"},ue),e.ListIndexesRequest=(ce.prototype.parent="",ce.prototype.filter="",ce.prototype.pageSize=0,ce.prototype.pageToken="",ce.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.ListIndexesRequest?e:(t=new s.google.firestore.admin.v1.ListIndexesRequest,null!=e.parent&&(t.parent=String(e.parent)),null!=e.filter&&(t.filter=String(e.filter)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),t)},ce.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.filter="",o.pageSize=0,o.pageToken=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=e.filter),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),o},ce.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ce.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListIndexesRequest"},ce),e.ListIndexesResponse=(ge.prototype.indexes=a.emptyArray,ge.prototype.nextPageToken="",ge.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ListIndexesResponse)return e;var t=new s.google.firestore.admin.v1.ListIndexesResponse;if(e.indexes){if(!Array.isArray(e.indexes))throw TypeError(".google.firestore.admin.v1.ListIndexesResponse.indexes: array expected");t.indexes=[];for(var o=0;o<e.indexes.length;++o){if("object"!=typeof e.indexes[o])throw TypeError(".google.firestore.admin.v1.ListIndexesResponse.indexes: object expected");t.indexes[o]=s.google.firestore.admin.v1.Index.fromObject(e.indexes[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},ge.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.indexes=[]),t.defaults&&(o.nextPageToken=""),e.indexes&&e.indexes.length){o.indexes=[];for(var r=0;r<e.indexes.length;++r)o.indexes[r]=s.google.firestore.admin.v1.Index.toObject(e.indexes[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},ge.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ge.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListIndexesResponse"},ge),e.GetIndexRequest=(fe.prototype.name="",fe.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.GetIndexRequest?e:(t=new s.google.firestore.admin.v1.GetIndexRequest,null!=e.name&&(t.name=String(e.name)),t)},fe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},fe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},fe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.GetIndexRequest"},fe),e.DeleteIndexRequest=(de.prototype.name="",de.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.DeleteIndexRequest?e:(t=new s.google.firestore.admin.v1.DeleteIndexRequest,null!=e.name&&(t.name=String(e.name)),t)},de.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},de.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},de.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.DeleteIndexRequest"},de),e.UpdateFieldRequest=(ye.prototype.field=null,ye.prototype.updateMask=null,ye.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.UpdateFieldRequest)return e;var t=new s.google.firestore.admin.v1.UpdateFieldRequest;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.admin.v1.UpdateFieldRequest.field: object expected");t.field=s.google.firestore.admin.v1.Field.fromObject(e.field)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.admin.v1.UpdateFieldRequest.updateMask: object expected");t.updateMask=s.google.protobuf.FieldMask.fromObject(e.updateMask)}return t},ye.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null,o.updateMask=null),null!=e.field&&e.hasOwnProperty("field")&&(o.field=s.google.firestore.admin.v1.Field.toObject(e.field,t)),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=s.google.protobuf.FieldMask.toObject(e.updateMask,t)),o},ye.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ye.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.UpdateFieldRequest"},ye),e.GetFieldRequest=(me.prototype.name="",me.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.GetFieldRequest?e:(t=new s.google.firestore.admin.v1.GetFieldRequest,null!=e.name&&(t.name=String(e.name)),t)},me.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},me.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},me.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.GetFieldRequest"},me),e.ListFieldsRequest=(be.prototype.parent="",be.prototype.filter="",be.prototype.pageSize=0,be.prototype.pageToken="",be.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.ListFieldsRequest?e:(t=new s.google.firestore.admin.v1.ListFieldsRequest,null!=e.parent&&(t.parent=String(e.parent)),null!=e.filter&&(t.filter=String(e.filter)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),t)},be.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.filter="",o.pageSize=0,o.pageToken=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=e.filter),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),o},be.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},be.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListFieldsRequest"},be),e.ListFieldsResponse=(Oe.prototype.fields=a.emptyArray,Oe.prototype.nextPageToken="",Oe.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ListFieldsResponse)return e;var t=new s.google.firestore.admin.v1.ListFieldsResponse;if(e.fields){if(!Array.isArray(e.fields))throw TypeError(".google.firestore.admin.v1.ListFieldsResponse.fields: array expected");t.fields=[];for(var o=0;o<e.fields.length;++o){if("object"!=typeof e.fields[o])throw TypeError(".google.firestore.admin.v1.ListFieldsResponse.fields: object expected");t.fields[o]=s.google.firestore.admin.v1.Field.fromObject(e.fields[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},Oe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fields=[]),t.defaults&&(o.nextPageToken=""),e.fields&&e.fields.length){o.fields=[];for(var r=0;r<e.fields.length;++r)o.fields[r]=s.google.firestore.admin.v1.Field.toObject(e.fields[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},Oe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Oe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListFieldsResponse"},Oe),e.ExportDocumentsRequest=(he.prototype.name="",he.prototype.collectionIds=a.emptyArray,he.prototype.outputUriPrefix="",he.prototype.namespaceIds=a.emptyArray,he.prototype.snapshotTime=null,he.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ExportDocumentsRequest)return e;var t=new s.google.firestore.admin.v1.ExportDocumentsRequest;if(null!=e.name&&(t.name=String(e.name)),e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.admin.v1.ExportDocumentsRequest.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}if(null!=e.outputUriPrefix&&(t.outputUriPrefix=String(e.outputUriPrefix)),e.namespaceIds){if(!Array.isArray(e.namespaceIds))throw TypeError(".google.firestore.admin.v1.ExportDocumentsRequest.namespaceIds: array expected");t.namespaceIds=[];for(o=0;o<e.namespaceIds.length;++o)t.namespaceIds[o]=String(e.namespaceIds[o])}if(null!=e.snapshotTime){if("object"!=typeof e.snapshotTime)throw TypeError(".google.firestore.admin.v1.ExportDocumentsRequest.snapshotTime: object expected");t.snapshotTime=s.google.protobuf.Timestamp.fromObject(e.snapshotTime)}return t},he.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[],o.namespaceIds=[]),t.defaults&&(o.name="",o.outputUriPrefix="",o.snapshotTime=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}if(null!=e.outputUriPrefix&&e.hasOwnProperty("outputUriPrefix")&&(o.outputUriPrefix=e.outputUriPrefix),e.namespaceIds&&e.namespaceIds.length){o.namespaceIds=[];for(r=0;r<e.namespaceIds.length;++r)o.namespaceIds[r]=e.namespaceIds[r]}return null!=e.snapshotTime&&e.hasOwnProperty("snapshotTime")&&(o.snapshotTime=s.google.protobuf.Timestamp.toObject(e.snapshotTime,t)),o},he.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},he.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ExportDocumentsRequest"},he),e.ImportDocumentsRequest=(Se.prototype.name="",Se.prototype.collectionIds=a.emptyArray,Se.prototype.inputUriPrefix="",Se.prototype.namespaceIds=a.emptyArray,Se.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ImportDocumentsRequest)return e;var t=new s.google.firestore.admin.v1.ImportDocumentsRequest;if(null!=e.name&&(t.name=String(e.name)),e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.admin.v1.ImportDocumentsRequest.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}if(null!=e.inputUriPrefix&&(t.inputUriPrefix=String(e.inputUriPrefix)),e.namespaceIds){if(!Array.isArray(e.namespaceIds))throw TypeError(".google.firestore.admin.v1.ImportDocumentsRequest.namespaceIds: array expected");t.namespaceIds=[];for(o=0;o<e.namespaceIds.length;++o)t.namespaceIds[o]=String(e.namespaceIds[o])}return t},Se.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[],o.namespaceIds=[]),t.defaults&&(o.name="",o.inputUriPrefix=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}if(null!=e.inputUriPrefix&&e.hasOwnProperty("inputUriPrefix")&&(o.inputUriPrefix=e.inputUriPrefix),e.namespaceIds&&e.namespaceIds.length){o.namespaceIds=[];for(r=0;r<e.namespaceIds.length;++r)o.namespaceIds[r]=e.namespaceIds[r]}return o},Se.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Se.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ImportDocumentsRequest"},Se),e.BulkDeleteDocumentsRequest=(ve.prototype.name="",ve.prototype.collectionIds=a.emptyArray,ve.prototype.namespaceIds=a.emptyArray,ve.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.BulkDeleteDocumentsRequest)return e;var t=new s.google.firestore.admin.v1.BulkDeleteDocumentsRequest;if(null!=e.name&&(t.name=String(e.name)),e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsRequest.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}if(e.namespaceIds){if(!Array.isArray(e.namespaceIds))throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsRequest.namespaceIds: array expected");t.namespaceIds=[];for(o=0;o<e.namespaceIds.length;++o)t.namespaceIds[o]=String(e.namespaceIds[o])}return t},ve.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[],o.namespaceIds=[]),t.defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}if(e.namespaceIds&&e.namespaceIds.length){o.namespaceIds=[];for(r=0;r<e.namespaceIds.length;++r)o.namespaceIds[r]=e.namespaceIds[r]}return o},ve.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ve.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.BulkDeleteDocumentsRequest"},ve),e.BulkDeleteDocumentsResponse=(Ee.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.BulkDeleteDocumentsResponse?e:new s.google.firestore.admin.v1.BulkDeleteDocumentsResponse},Ee.toObject=function(){return{}},Ee.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ee.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.BulkDeleteDocumentsResponse"},Ee),e.GetBackupRequest=(Te.prototype.name="",Te.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.GetBackupRequest?e:(t=new s.google.firestore.admin.v1.GetBackupRequest,null!=e.name&&(t.name=String(e.name)),t)},Te.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Te.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Te.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.GetBackupRequest"},Te),e.ListBackupsRequest=(Ie.prototype.parent="",Ie.prototype.filter="",Ie.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.ListBackupsRequest?e:(t=new s.google.firestore.admin.v1.ListBackupsRequest,null!=e.parent&&(t.parent=String(e.parent)),null!=e.filter&&(t.filter=String(e.filter)),t)},Ie.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.filter=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=e.filter),o},Ie.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ie.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListBackupsRequest"},Ie),e.ListBackupsResponse=(je.prototype.backups=a.emptyArray,je.prototype.unreachable=a.emptyArray,je.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ListBackupsResponse)return e;var t=new s.google.firestore.admin.v1.ListBackupsResponse;if(e.backups){if(!Array.isArray(e.backups))throw TypeError(".google.firestore.admin.v1.ListBackupsResponse.backups: array expected");t.backups=[];for(var o=0;o<e.backups.length;++o){if("object"!=typeof e.backups[o])throw TypeError(".google.firestore.admin.v1.ListBackupsResponse.backups: object expected");t.backups[o]=s.google.firestore.admin.v1.Backup.fromObject(e.backups[o])}}if(e.unreachable){if(!Array.isArray(e.unreachable))throw TypeError(".google.firestore.admin.v1.ListBackupsResponse.unreachable: array expected");t.unreachable=[];for(o=0;o<e.unreachable.length;++o)t.unreachable[o]=String(e.unreachable[o])}return t},je.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.backups=[],o.unreachable=[]),e.backups&&e.backups.length){o.backups=[];for(var r=0;r<e.backups.length;++r)o.backups[r]=s.google.firestore.admin.v1.Backup.toObject(e.backups[r],t)}if(e.unreachable&&e.unreachable.length){o.unreachable=[];for(r=0;r<e.unreachable.length;++r)o.unreachable[r]=e.unreachable[r]}return o},je.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},je.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ListBackupsResponse"},je),e.DeleteBackupRequest=(Ne.prototype.name="",Ne.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.DeleteBackupRequest?e:(t=new s.google.firestore.admin.v1.DeleteBackupRequest,null!=e.name&&(t.name=String(e.name)),t)},Ne.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Ne.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ne.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.DeleteBackupRequest"},Ne),e.RestoreDatabaseRequest=(Pe.prototype.parent="",Pe.prototype.databaseId="",Pe.prototype.backup="",Pe.prototype.encryptionConfig=null,Pe.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.RestoreDatabaseRequest)return e;var t=new s.google.firestore.admin.v1.RestoreDatabaseRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.databaseId&&(t.databaseId=String(e.databaseId)),null!=e.backup&&(t.backup=String(e.backup)),null!=e.encryptionConfig){if("object"!=typeof e.encryptionConfig)throw TypeError(".google.firestore.admin.v1.RestoreDatabaseRequest.encryptionConfig: object expected");t.encryptionConfig=s.google.firestore.admin.v1.Database.EncryptionConfig.fromObject(e.encryptionConfig)}return t},Pe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.databaseId="",o.backup="",o.encryptionConfig=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.databaseId&&e.hasOwnProperty("databaseId")&&(o.databaseId=e.databaseId),null!=e.backup&&e.hasOwnProperty("backup")&&(o.backup=e.backup),null!=e.encryptionConfig&&e.hasOwnProperty("encryptionConfig")&&(o.encryptionConfig=s.google.firestore.admin.v1.Database.EncryptionConfig.toObject(e.encryptionConfig,t)),o},Pe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Pe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.RestoreDatabaseRequest"},Pe),e.IndexOperationMetadata=(d.prototype.startTime=null,d.prototype.endTime=null,d.prototype.index="",d.prototype.state=0,d.prototype.progressDocuments=null,d.prototype.progressBytes=null,d.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.IndexOperationMetadata)return e;var t=new s.google.firestore.admin.v1.IndexOperationMetadata;if(null!=e.startTime){if("object"!=typeof e.startTime)throw TypeError(".google.firestore.admin.v1.IndexOperationMetadata.startTime: object expected");t.startTime=s.google.protobuf.Timestamp.fromObject(e.startTime)}if(null!=e.endTime){if("object"!=typeof e.endTime)throw TypeError(".google.firestore.admin.v1.IndexOperationMetadata.endTime: object expected");t.endTime=s.google.protobuf.Timestamp.fromObject(e.endTime)}switch(null!=e.index&&(t.index=String(e.index)),e.state){default:"number"==typeof e.state&&(t.state=e.state);break;case"OPERATION_STATE_UNSPECIFIED":case 0:t.state=0;break;case"INITIALIZING":case 1:t.state=1;break;case"PROCESSING":case 2:t.state=2;break;case"CANCELLING":case 3:t.state=3;break;case"FINALIZING":case 4:t.state=4;break;case"SUCCESSFUL":case 5:t.state=5;break;case"FAILED":case 6:t.state=6;break;case"CANCELLED":case 7:t.state=7}if(null!=e.progressDocuments){if("object"!=typeof e.progressDocuments)throw TypeError(".google.firestore.admin.v1.IndexOperationMetadata.progressDocuments: object expected");t.progressDocuments=s.google.firestore.admin.v1.Progress.fromObject(e.progressDocuments)}if(null!=e.progressBytes){if("object"!=typeof e.progressBytes)throw TypeError(".google.firestore.admin.v1.IndexOperationMetadata.progressBytes: object expected");t.progressBytes=s.google.firestore.admin.v1.Progress.fromObject(e.progressBytes)}return t},d.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.startTime=null,o.endTime=null,o.index="",o.state=t.enums===String?"OPERATION_STATE_UNSPECIFIED":0,o.progressDocuments=null,o.progressBytes=null),null!=e.startTime&&e.hasOwnProperty("startTime")&&(o.startTime=s.google.protobuf.Timestamp.toObject(e.startTime,t)),null!=e.endTime&&e.hasOwnProperty("endTime")&&(o.endTime=s.google.protobuf.Timestamp.toObject(e.endTime,t)),null!=e.index&&e.hasOwnProperty("index")&&(o.index=e.index),null!=e.state&&e.hasOwnProperty("state")&&(o.state=t.enums!==String||void 0===s.google.firestore.admin.v1.OperationState[e.state]?e.state:s.google.firestore.admin.v1.OperationState[e.state]),null!=e.progressDocuments&&e.hasOwnProperty("progressDocuments")&&(o.progressDocuments=s.google.firestore.admin.v1.Progress.toObject(e.progressDocuments,t)),null!=e.progressBytes&&e.hasOwnProperty("progressBytes")&&(o.progressBytes=s.google.firestore.admin.v1.Progress.toObject(e.progressBytes,t)),o},d.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},d.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.IndexOperationMetadata"},d),e.FieldOperationMetadata=(y.prototype.startTime=null,y.prototype.endTime=null,y.prototype.field="",y.prototype.indexConfigDeltas=a.emptyArray,y.prototype.state=0,y.prototype.progressDocuments=null,y.prototype.progressBytes=null,y.prototype.ttlConfigDelta=null,y.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.FieldOperationMetadata)return e;var t=new s.google.firestore.admin.v1.FieldOperationMetadata;if(null!=e.startTime){if("object"!=typeof e.startTime)throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.startTime: object expected");t.startTime=s.google.protobuf.Timestamp.fromObject(e.startTime)}if(null!=e.endTime){if("object"!=typeof e.endTime)throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.endTime: object expected");t.endTime=s.google.protobuf.Timestamp.fromObject(e.endTime)}if(null!=e.field&&(t.field=String(e.field)),e.indexConfigDeltas){if(!Array.isArray(e.indexConfigDeltas))throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.indexConfigDeltas: array expected");t.indexConfigDeltas=[];for(var o=0;o<e.indexConfigDeltas.length;++o){if("object"!=typeof e.indexConfigDeltas[o])throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.indexConfigDeltas: object expected");t.indexConfigDeltas[o]=s.google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta.fromObject(e.indexConfigDeltas[o])}}switch(e.state){default:"number"==typeof e.state&&(t.state=e.state);break;case"OPERATION_STATE_UNSPECIFIED":case 0:t.state=0;break;case"INITIALIZING":case 1:t.state=1;break;case"PROCESSING":case 2:t.state=2;break;case"CANCELLING":case 3:t.state=3;break;case"FINALIZING":case 4:t.state=4;break;case"SUCCESSFUL":case 5:t.state=5;break;case"FAILED":case 6:t.state=6;break;case"CANCELLED":case 7:t.state=7}if(null!=e.progressDocuments){if("object"!=typeof e.progressDocuments)throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.progressDocuments: object expected");t.progressDocuments=s.google.firestore.admin.v1.Progress.fromObject(e.progressDocuments)}if(null!=e.progressBytes){if("object"!=typeof e.progressBytes)throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.progressBytes: object expected");t.progressBytes=s.google.firestore.admin.v1.Progress.fromObject(e.progressBytes)}if(null!=e.ttlConfigDelta){if("object"!=typeof e.ttlConfigDelta)throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.ttlConfigDelta: object expected");t.ttlConfigDelta=s.google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta.fromObject(e.ttlConfigDelta)}return t},y.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.indexConfigDeltas=[]),t.defaults&&(o.startTime=null,o.endTime=null,o.field="",o.state=t.enums===String?"OPERATION_STATE_UNSPECIFIED":0,o.progressDocuments=null,o.progressBytes=null,o.ttlConfigDelta=null),null!=e.startTime&&e.hasOwnProperty("startTime")&&(o.startTime=s.google.protobuf.Timestamp.toObject(e.startTime,t)),null!=e.endTime&&e.hasOwnProperty("endTime")&&(o.endTime=s.google.protobuf.Timestamp.toObject(e.endTime,t)),null!=e.field&&e.hasOwnProperty("field")&&(o.field=e.field),e.indexConfigDeltas&&e.indexConfigDeltas.length){o.indexConfigDeltas=[];for(var r=0;r<e.indexConfigDeltas.length;++r)o.indexConfigDeltas[r]=s.google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta.toObject(e.indexConfigDeltas[r],t)}return null!=e.state&&e.hasOwnProperty("state")&&(o.state=t.enums!==String||void 0===s.google.firestore.admin.v1.OperationState[e.state]?e.state:s.google.firestore.admin.v1.OperationState[e.state]),null!=e.progressDocuments&&e.hasOwnProperty("progressDocuments")&&(o.progressDocuments=s.google.firestore.admin.v1.Progress.toObject(e.progressDocuments,t)),null!=e.progressBytes&&e.hasOwnProperty("progressBytes")&&(o.progressBytes=s.google.firestore.admin.v1.Progress.toObject(e.progressBytes,t)),null!=e.ttlConfigDelta&&e.hasOwnProperty("ttlConfigDelta")&&(o.ttlConfigDelta=s.google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta.toObject(e.ttlConfigDelta,t)),o},y.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},y.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.FieldOperationMetadata"},y.IndexConfigDelta=(De.prototype.changeType=0,De.prototype.index=null,De.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta)return e;var t=new s.google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta;switch(e.changeType){default:"number"==typeof e.changeType&&(t.changeType=e.changeType);break;case"CHANGE_TYPE_UNSPECIFIED":case 0:t.changeType=0;break;case"ADD":case 1:t.changeType=1;break;case"REMOVE":case 2:t.changeType=2}if(null!=e.index){if("object"!=typeof e.index)throw TypeError(".google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta.index: object expected");t.index=s.google.firestore.admin.v1.Index.fromObject(e.index)}return t},De.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.changeType=t.enums===String?"CHANGE_TYPE_UNSPECIFIED":0,o.index=null),null!=e.changeType&&e.hasOwnProperty("changeType")&&(o.changeType=t.enums!==String||void 0===s.google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta.ChangeType[e.changeType]?e.changeType:s.google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta.ChangeType[e.changeType]),null!=e.index&&e.hasOwnProperty("index")&&(o.index=s.google.firestore.admin.v1.Index.toObject(e.index,t)),o},De.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},De.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.FieldOperationMetadata.IndexConfigDelta"},De.ChangeType=(t={},(i=Object.create(t))[t[0]="CHANGE_TYPE_UNSPECIFIED"]="CHANGE_TYPE_UNSPECIFIED",i[t[1]="ADD"]="ADD",i[t[2]="REMOVE"]="REMOVE",i),De),y.TtlConfigDelta=(we.prototype.changeType=0,we.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta)return e;var t=new s.google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta;switch(e.changeType){default:"number"==typeof e.changeType&&(t.changeType=e.changeType);break;case"CHANGE_TYPE_UNSPECIFIED":case 0:t.changeType=0;break;case"ADD":case 1:t.changeType=1;break;case"REMOVE":case 2:t.changeType=2}return t},we.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.changeType=t.enums===String?"CHANGE_TYPE_UNSPECIFIED":0),null!=e.changeType&&e.hasOwnProperty("changeType")&&(o.changeType=t.enums!==String||void 0===s.google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta.ChangeType[e.changeType]?e.changeType:s.google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta.ChangeType[e.changeType]),o},we.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},we.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.FieldOperationMetadata.TtlConfigDelta"},we.ChangeType=(t={},(i=Object.create(t))[t[0]="CHANGE_TYPE_UNSPECIFIED"]="CHANGE_TYPE_UNSPECIFIED",i[t[1]="ADD"]="ADD",i[t[2]="REMOVE"]="REMOVE",i),we),y),e.ExportDocumentsMetadata=(m.prototype.startTime=null,m.prototype.endTime=null,m.prototype.operationState=0,m.prototype.progressDocuments=null,m.prototype.progressBytes=null,m.prototype.collectionIds=a.emptyArray,m.prototype.outputUriPrefix="",m.prototype.namespaceIds=a.emptyArray,m.prototype.snapshotTime=null,m.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ExportDocumentsMetadata)return e;var t=new s.google.firestore.admin.v1.ExportDocumentsMetadata;if(null!=e.startTime){if("object"!=typeof e.startTime)throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.startTime: object expected");t.startTime=s.google.protobuf.Timestamp.fromObject(e.startTime)}if(null!=e.endTime){if("object"!=typeof e.endTime)throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.endTime: object expected");t.endTime=s.google.protobuf.Timestamp.fromObject(e.endTime)}switch(e.operationState){default:"number"==typeof e.operationState&&(t.operationState=e.operationState);break;case"OPERATION_STATE_UNSPECIFIED":case 0:t.operationState=0;break;case"INITIALIZING":case 1:t.operationState=1;break;case"PROCESSING":case 2:t.operationState=2;break;case"CANCELLING":case 3:t.operationState=3;break;case"FINALIZING":case 4:t.operationState=4;break;case"SUCCESSFUL":case 5:t.operationState=5;break;case"FAILED":case 6:t.operationState=6;break;case"CANCELLED":case 7:t.operationState=7}if(null!=e.progressDocuments){if("object"!=typeof e.progressDocuments)throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.progressDocuments: object expected");t.progressDocuments=s.google.firestore.admin.v1.Progress.fromObject(e.progressDocuments)}if(null!=e.progressBytes){if("object"!=typeof e.progressBytes)throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.progressBytes: object expected");t.progressBytes=s.google.firestore.admin.v1.Progress.fromObject(e.progressBytes)}if(e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}if(null!=e.outputUriPrefix&&(t.outputUriPrefix=String(e.outputUriPrefix)),e.namespaceIds){if(!Array.isArray(e.namespaceIds))throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.namespaceIds: array expected");t.namespaceIds=[];for(o=0;o<e.namespaceIds.length;++o)t.namespaceIds[o]=String(e.namespaceIds[o])}if(null!=e.snapshotTime){if("object"!=typeof e.snapshotTime)throw TypeError(".google.firestore.admin.v1.ExportDocumentsMetadata.snapshotTime: object expected");t.snapshotTime=s.google.protobuf.Timestamp.fromObject(e.snapshotTime)}return t},m.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[],o.namespaceIds=[]),t.defaults&&(o.startTime=null,o.endTime=null,o.operationState=t.enums===String?"OPERATION_STATE_UNSPECIFIED":0,o.progressDocuments=null,o.progressBytes=null,o.outputUriPrefix="",o.snapshotTime=null),null!=e.startTime&&e.hasOwnProperty("startTime")&&(o.startTime=s.google.protobuf.Timestamp.toObject(e.startTime,t)),null!=e.endTime&&e.hasOwnProperty("endTime")&&(o.endTime=s.google.protobuf.Timestamp.toObject(e.endTime,t)),null!=e.operationState&&e.hasOwnProperty("operationState")&&(o.operationState=t.enums!==String||void 0===s.google.firestore.admin.v1.OperationState[e.operationState]?e.operationState:s.google.firestore.admin.v1.OperationState[e.operationState]),null!=e.progressDocuments&&e.hasOwnProperty("progressDocuments")&&(o.progressDocuments=s.google.firestore.admin.v1.Progress.toObject(e.progressDocuments,t)),null!=e.progressBytes&&e.hasOwnProperty("progressBytes")&&(o.progressBytes=s.google.firestore.admin.v1.Progress.toObject(e.progressBytes,t)),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}if(null!=e.outputUriPrefix&&e.hasOwnProperty("outputUriPrefix")&&(o.outputUriPrefix=e.outputUriPrefix),e.namespaceIds&&e.namespaceIds.length){o.namespaceIds=[];for(r=0;r<e.namespaceIds.length;++r)o.namespaceIds[r]=e.namespaceIds[r]}return null!=e.snapshotTime&&e.hasOwnProperty("snapshotTime")&&(o.snapshotTime=s.google.protobuf.Timestamp.toObject(e.snapshotTime,t)),o},m.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},m.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ExportDocumentsMetadata"},m),e.ImportDocumentsMetadata=(b.prototype.startTime=null,b.prototype.endTime=null,b.prototype.operationState=0,b.prototype.progressDocuments=null,b.prototype.progressBytes=null,b.prototype.collectionIds=a.emptyArray,b.prototype.inputUriPrefix="",b.prototype.namespaceIds=a.emptyArray,b.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.ImportDocumentsMetadata)return e;var t=new s.google.firestore.admin.v1.ImportDocumentsMetadata;if(null!=e.startTime){if("object"!=typeof e.startTime)throw TypeError(".google.firestore.admin.v1.ImportDocumentsMetadata.startTime: object expected");t.startTime=s.google.protobuf.Timestamp.fromObject(e.startTime)}if(null!=e.endTime){if("object"!=typeof e.endTime)throw TypeError(".google.firestore.admin.v1.ImportDocumentsMetadata.endTime: object expected");t.endTime=s.google.protobuf.Timestamp.fromObject(e.endTime)}switch(e.operationState){default:"number"==typeof e.operationState&&(t.operationState=e.operationState);break;case"OPERATION_STATE_UNSPECIFIED":case 0:t.operationState=0;break;case"INITIALIZING":case 1:t.operationState=1;break;case"PROCESSING":case 2:t.operationState=2;break;case"CANCELLING":case 3:t.operationState=3;break;case"FINALIZING":case 4:t.operationState=4;break;case"SUCCESSFUL":case 5:t.operationState=5;break;case"FAILED":case 6:t.operationState=6;break;case"CANCELLED":case 7:t.operationState=7}if(null!=e.progressDocuments){if("object"!=typeof e.progressDocuments)throw TypeError(".google.firestore.admin.v1.ImportDocumentsMetadata.progressDocuments: object expected");t.progressDocuments=s.google.firestore.admin.v1.Progress.fromObject(e.progressDocuments)}if(null!=e.progressBytes){if("object"!=typeof e.progressBytes)throw TypeError(".google.firestore.admin.v1.ImportDocumentsMetadata.progressBytes: object expected");t.progressBytes=s.google.firestore.admin.v1.Progress.fromObject(e.progressBytes)}if(e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.admin.v1.ImportDocumentsMetadata.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}if(null!=e.inputUriPrefix&&(t.inputUriPrefix=String(e.inputUriPrefix)),e.namespaceIds){if(!Array.isArray(e.namespaceIds))throw TypeError(".google.firestore.admin.v1.ImportDocumentsMetadata.namespaceIds: array expected");t.namespaceIds=[];for(o=0;o<e.namespaceIds.length;++o)t.namespaceIds[o]=String(e.namespaceIds[o])}return t},b.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[],o.namespaceIds=[]),t.defaults&&(o.startTime=null,o.endTime=null,o.operationState=t.enums===String?"OPERATION_STATE_UNSPECIFIED":0,o.progressDocuments=null,o.progressBytes=null,o.inputUriPrefix=""),null!=e.startTime&&e.hasOwnProperty("startTime")&&(o.startTime=s.google.protobuf.Timestamp.toObject(e.startTime,t)),null!=e.endTime&&e.hasOwnProperty("endTime")&&(o.endTime=s.google.protobuf.Timestamp.toObject(e.endTime,t)),null!=e.operationState&&e.hasOwnProperty("operationState")&&(o.operationState=t.enums!==String||void 0===s.google.firestore.admin.v1.OperationState[e.operationState]?e.operationState:s.google.firestore.admin.v1.OperationState[e.operationState]),null!=e.progressDocuments&&e.hasOwnProperty("progressDocuments")&&(o.progressDocuments=s.google.firestore.admin.v1.Progress.toObject(e.progressDocuments,t)),null!=e.progressBytes&&e.hasOwnProperty("progressBytes")&&(o.progressBytes=s.google.firestore.admin.v1.Progress.toObject(e.progressBytes,t)),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}if(null!=e.inputUriPrefix&&e.hasOwnProperty("inputUriPrefix")&&(o.inputUriPrefix=e.inputUriPrefix),e.namespaceIds&&e.namespaceIds.length){o.namespaceIds=[];for(r=0;r<e.namespaceIds.length;++r)o.namespaceIds[r]=e.namespaceIds[r]}return o},b.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},b.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ImportDocumentsMetadata"},b),e.BulkDeleteDocumentsMetadata=(O.prototype.startTime=null,O.prototype.endTime=null,O.prototype.operationState=0,O.prototype.progressDocuments=null,O.prototype.progressBytes=null,O.prototype.collectionIds=a.emptyArray,O.prototype.namespaceIds=a.emptyArray,O.prototype.snapshotTime=null,O.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.BulkDeleteDocumentsMetadata)return e;var t=new s.google.firestore.admin.v1.BulkDeleteDocumentsMetadata;if(null!=e.startTime){if("object"!=typeof e.startTime)throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.startTime: object expected");t.startTime=s.google.protobuf.Timestamp.fromObject(e.startTime)}if(null!=e.endTime){if("object"!=typeof e.endTime)throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.endTime: object expected");t.endTime=s.google.protobuf.Timestamp.fromObject(e.endTime)}switch(e.operationState){default:"number"==typeof e.operationState&&(t.operationState=e.operationState);break;case"OPERATION_STATE_UNSPECIFIED":case 0:t.operationState=0;break;case"INITIALIZING":case 1:t.operationState=1;break;case"PROCESSING":case 2:t.operationState=2;break;case"CANCELLING":case 3:t.operationState=3;break;case"FINALIZING":case 4:t.operationState=4;break;case"SUCCESSFUL":case 5:t.operationState=5;break;case"FAILED":case 6:t.operationState=6;break;case"CANCELLED":case 7:t.operationState=7}if(null!=e.progressDocuments){if("object"!=typeof e.progressDocuments)throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.progressDocuments: object expected");t.progressDocuments=s.google.firestore.admin.v1.Progress.fromObject(e.progressDocuments)}if(null!=e.progressBytes){if("object"!=typeof e.progressBytes)throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.progressBytes: object expected");t.progressBytes=s.google.firestore.admin.v1.Progress.fromObject(e.progressBytes)}if(e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}if(e.namespaceIds){if(!Array.isArray(e.namespaceIds))throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.namespaceIds: array expected");t.namespaceIds=[];for(o=0;o<e.namespaceIds.length;++o)t.namespaceIds[o]=String(e.namespaceIds[o])}if(null!=e.snapshotTime){if("object"!=typeof e.snapshotTime)throw TypeError(".google.firestore.admin.v1.BulkDeleteDocumentsMetadata.snapshotTime: object expected");t.snapshotTime=s.google.protobuf.Timestamp.fromObject(e.snapshotTime)}return t},O.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[],o.namespaceIds=[]),t.defaults&&(o.startTime=null,o.endTime=null,o.operationState=t.enums===String?"OPERATION_STATE_UNSPECIFIED":0,o.progressDocuments=null,o.progressBytes=null,o.snapshotTime=null),null!=e.startTime&&e.hasOwnProperty("startTime")&&(o.startTime=s.google.protobuf.Timestamp.toObject(e.startTime,t)),null!=e.endTime&&e.hasOwnProperty("endTime")&&(o.endTime=s.google.protobuf.Timestamp.toObject(e.endTime,t)),null!=e.operationState&&e.hasOwnProperty("operationState")&&(o.operationState=t.enums!==String||void 0===s.google.firestore.admin.v1.OperationState[e.operationState]?e.operationState:s.google.firestore.admin.v1.OperationState[e.operationState]),null!=e.progressDocuments&&e.hasOwnProperty("progressDocuments")&&(o.progressDocuments=s.google.firestore.admin.v1.Progress.toObject(e.progressDocuments,t)),null!=e.progressBytes&&e.hasOwnProperty("progressBytes")&&(o.progressBytes=s.google.firestore.admin.v1.Progress.toObject(e.progressBytes,t)),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}if(e.namespaceIds&&e.namespaceIds.length){o.namespaceIds=[];for(r=0;r<e.namespaceIds.length;++r)o.namespaceIds[r]=e.namespaceIds[r]}return null!=e.snapshotTime&&e.hasOwnProperty("snapshotTime")&&(o.snapshotTime=s.google.protobuf.Timestamp.toObject(e.snapshotTime,t)),o},O.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},O.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.BulkDeleteDocumentsMetadata"},O),e.ExportDocumentsResponse=(ke.prototype.outputUriPrefix="",ke.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.ExportDocumentsResponse?e:(t=new s.google.firestore.admin.v1.ExportDocumentsResponse,null!=e.outputUriPrefix&&(t.outputUriPrefix=String(e.outputUriPrefix)),t)},ke.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.outputUriPrefix=""),null!=e.outputUriPrefix&&e.hasOwnProperty("outputUriPrefix")&&(o.outputUriPrefix=e.outputUriPrefix),o},ke.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ke.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.ExportDocumentsResponse"},ke),e.RestoreDatabaseMetadata=(h.prototype.startTime=null,h.prototype.endTime=null,h.prototype.operationState=0,h.prototype.database="",h.prototype.backup="",h.prototype.progressPercentage=null,h.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.RestoreDatabaseMetadata)return e;var t=new s.google.firestore.admin.v1.RestoreDatabaseMetadata;if(null!=e.startTime){if("object"!=typeof e.startTime)throw TypeError(".google.firestore.admin.v1.RestoreDatabaseMetadata.startTime: object expected");t.startTime=s.google.protobuf.Timestamp.fromObject(e.startTime)}if(null!=e.endTime){if("object"!=typeof e.endTime)throw TypeError(".google.firestore.admin.v1.RestoreDatabaseMetadata.endTime: object expected");t.endTime=s.google.protobuf.Timestamp.fromObject(e.endTime)}switch(e.operationState){default:"number"==typeof e.operationState&&(t.operationState=e.operationState);break;case"OPERATION_STATE_UNSPECIFIED":case 0:t.operationState=0;break;case"INITIALIZING":case 1:t.operationState=1;break;case"PROCESSING":case 2:t.operationState=2;break;case"CANCELLING":case 3:t.operationState=3;break;case"FINALIZING":case 4:t.operationState=4;break;case"SUCCESSFUL":case 5:t.operationState=5;break;case"FAILED":case 6:t.operationState=6;break;case"CANCELLED":case 7:t.operationState=7}if(null!=e.database&&(t.database=String(e.database)),null!=e.backup&&(t.backup=String(e.backup)),null!=e.progressPercentage){if("object"!=typeof e.progressPercentage)throw TypeError(".google.firestore.admin.v1.RestoreDatabaseMetadata.progressPercentage: object expected");t.progressPercentage=s.google.firestore.admin.v1.Progress.fromObject(e.progressPercentage)}return t},h.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.startTime=null,o.endTime=null,o.operationState=t.enums===String?"OPERATION_STATE_UNSPECIFIED":0,o.database="",o.backup="",o.progressPercentage=null),null!=e.startTime&&e.hasOwnProperty("startTime")&&(o.startTime=s.google.protobuf.Timestamp.toObject(e.startTime,t)),null!=e.endTime&&e.hasOwnProperty("endTime")&&(o.endTime=s.google.protobuf.Timestamp.toObject(e.endTime,t)),null!=e.operationState&&e.hasOwnProperty("operationState")&&(o.operationState=t.enums!==String||void 0===s.google.firestore.admin.v1.OperationState[e.operationState]?e.operationState:s.google.firestore.admin.v1.OperationState[e.operationState]),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),null!=e.backup&&e.hasOwnProperty("backup")&&(o.backup=e.backup),null!=e.progressPercentage&&e.hasOwnProperty("progressPercentage")&&(o.progressPercentage=s.google.firestore.admin.v1.Progress.toObject(e.progressPercentage,t)),o},h.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},h.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.RestoreDatabaseMetadata"},h),e.Progress=(Re.prototype.estimatedWork=a.Long?a.Long.fromBits(0,0,!1):0,Re.prototype.completedWork=a.Long?a.Long.fromBits(0,0,!1):0,Re.fromObject=function(e){var t;return e instanceof s.google.firestore.admin.v1.Progress?e:(t=new s.google.firestore.admin.v1.Progress,null!=e.estimatedWork&&(a.Long?(t.estimatedWork=a.Long.fromValue(e.estimatedWork)).unsigned=!1:"string"==typeof e.estimatedWork?t.estimatedWork=parseInt(e.estimatedWork,10):"number"==typeof e.estimatedWork?t.estimatedWork=e.estimatedWork:"object"==typeof e.estimatedWork&&(t.estimatedWork=new a.LongBits(e.estimatedWork.low>>>0,e.estimatedWork.high>>>0).toNumber())),null!=e.completedWork&&(a.Long?(t.completedWork=a.Long.fromValue(e.completedWork)).unsigned=!1:"string"==typeof e.completedWork?t.completedWork=parseInt(e.completedWork,10):"number"==typeof e.completedWork?t.completedWork=e.completedWork:"object"==typeof e.completedWork&&(t.completedWork=new a.LongBits(e.completedWork.low>>>0,e.completedWork.high>>>0).toNumber())),t)},Re.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(a.Long?(o=new a.Long(0,0,!1),r.estimatedWork=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.estimatedWork=t.longs===String?"0":0,a.Long?(o=new a.Long(0,0,!1),r.completedWork=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.completedWork=t.longs===String?"0":0),null!=e.estimatedWork&&e.hasOwnProperty("estimatedWork")&&("number"==typeof e.estimatedWork?r.estimatedWork=t.longs===String?String(e.estimatedWork):e.estimatedWork:r.estimatedWork=t.longs===String?a.Long.prototype.toString.call(e.estimatedWork):t.longs===Number?new a.LongBits(e.estimatedWork.low>>>0,e.estimatedWork.high>>>0).toNumber():e.estimatedWork),null!=e.completedWork&&e.hasOwnProperty("completedWork")&&("number"==typeof e.completedWork?r.completedWork=t.longs===String?String(e.completedWork):e.completedWork:r.completedWork=t.longs===String?a.Long.prototype.toString.call(e.completedWork):t.longs===Number?new a.LongBits(e.completedWork.low>>>0,e.completedWork.high>>>0).toNumber():e.completedWork),r},Re.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Re.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.Progress"},Re),e.OperationState=(t={},(i=Object.create(t))[t[0]="OPERATION_STATE_UNSPECIFIED"]="OPERATION_STATE_UNSPECIFIED",i[t[1]="INITIALIZING"]="INITIALIZING",i[t[2]="PROCESSING"]="PROCESSING",i[t[3]="CANCELLING"]="CANCELLING",i[t[4]="FINALIZING"]="FINALIZING",i[t[5]="SUCCESSFUL"]="SUCCESSFUL",i[t[6]="FAILED"]="FAILED",i[t[7]="CANCELLED"]="CANCELLED",i),e.BackupSchedule=(S.prototype.name="",S.prototype.createTime=null,S.prototype.updateTime=null,S.prototype.retention=null,S.prototype.dailyRecurrence=null,S.prototype.weeklyRecurrence=null,Object.defineProperty(S.prototype,"recurrence",{get:a.oneOfGetter(t=["dailyRecurrence","weeklyRecurrence"]),set:a.oneOfSetter(t)}),S.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.BackupSchedule)return e;var t=new s.google.firestore.admin.v1.BackupSchedule;if(null!=e.name&&(t.name=String(e.name)),null!=e.createTime){if("object"!=typeof e.createTime)throw TypeError(".google.firestore.admin.v1.BackupSchedule.createTime: object expected");t.createTime=s.google.protobuf.Timestamp.fromObject(e.createTime)}if(null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.admin.v1.BackupSchedule.updateTime: object expected");t.updateTime=s.google.protobuf.Timestamp.fromObject(e.updateTime)}if(null!=e.retention){if("object"!=typeof e.retention)throw TypeError(".google.firestore.admin.v1.BackupSchedule.retention: object expected");t.retention=s.google.protobuf.Duration.fromObject(e.retention)}if(null!=e.dailyRecurrence){if("object"!=typeof e.dailyRecurrence)throw TypeError(".google.firestore.admin.v1.BackupSchedule.dailyRecurrence: object expected");t.dailyRecurrence=s.google.firestore.admin.v1.DailyRecurrence.fromObject(e.dailyRecurrence)}if(null!=e.weeklyRecurrence){if("object"!=typeof e.weeklyRecurrence)throw TypeError(".google.firestore.admin.v1.BackupSchedule.weeklyRecurrence: object expected");t.weeklyRecurrence=s.google.firestore.admin.v1.WeeklyRecurrence.fromObject(e.weeklyRecurrence)}return t},S.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.createTime=null,o.retention=null,o.updateTime=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.createTime&&e.hasOwnProperty("createTime")&&(o.createTime=s.google.protobuf.Timestamp.toObject(e.createTime,t)),null!=e.retention&&e.hasOwnProperty("retention")&&(o.retention=s.google.protobuf.Duration.toObject(e.retention,t)),null!=e.dailyRecurrence&&e.hasOwnProperty("dailyRecurrence")&&(o.dailyRecurrence=s.google.firestore.admin.v1.DailyRecurrence.toObject(e.dailyRecurrence,t),t.oneofs)&&(o.recurrence="dailyRecurrence"),null!=e.weeklyRecurrence&&e.hasOwnProperty("weeklyRecurrence")&&(o.weeklyRecurrence=s.google.firestore.admin.v1.WeeklyRecurrence.toObject(e.weeklyRecurrence,t),t.oneofs)&&(o.recurrence="weeklyRecurrence"),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(o.updateTime=s.google.protobuf.Timestamp.toObject(e.updateTime,t)),o},S.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},S.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.BackupSchedule"},S),e.DailyRecurrence=(Ce.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.DailyRecurrence?e:new s.google.firestore.admin.v1.DailyRecurrence},Ce.toObject=function(){return{}},Ce.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ce.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.DailyRecurrence"},Ce),e.WeeklyRecurrence=(xe.prototype.day=0,xe.fromObject=function(e){if(e instanceof s.google.firestore.admin.v1.WeeklyRecurrence)return e;var t=new s.google.firestore.admin.v1.WeeklyRecurrence;switch(e.day){default:"number"==typeof e.day&&(t.day=e.day);break;case"DAY_OF_WEEK_UNSPECIFIED":case 0:t.day=0;break;case"MONDAY":case 1:t.day=1;break;case"TUESDAY":case 2:t.day=2;break;case"WEDNESDAY":case 3:t.day=3;break;case"THURSDAY":case 4:t.day=4;break;case"FRIDAY":case 5:t.day=5;break;case"SATURDAY":case 6:t.day=6;break;case"SUNDAY":case 7:t.day=7}return t},xe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.day=t.enums===String?"DAY_OF_WEEK_UNSPECIFIED":0),null!=e.day&&e.hasOwnProperty("day")&&(o.day=t.enums!==String||void 0===s.google.type.DayOfWeek[e.day]?e.day:s.google.type.DayOfWeek[e.day]),o},xe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},xe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.WeeklyRecurrence"},xe),e.LocationMetadata=(Ae.fromObject=function(e){return e instanceof s.google.firestore.admin.v1.LocationMetadata?e:new s.google.firestore.admin.v1.LocationMetadata},Ae.toObject=function(){return{}},Ae.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ae.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.admin.v1.LocationMetadata"},Ae),e),o),n),L.api=((i={}).FieldBehavior=(t={},(e=Object.create(t))[t[0]="FIELD_BEHAVIOR_UNSPECIFIED"]="FIELD_BEHAVIOR_UNSPECIFIED",e[t[1]="OPTIONAL"]="OPTIONAL",e[t[2]="REQUIRED"]="REQUIRED",e[t[3]="OUTPUT_ONLY"]="OUTPUT_ONLY",e[t[4]="INPUT_ONLY"]="INPUT_ONLY",e[t[5]="IMMUTABLE"]="IMMUTABLE",e[t[6]="UNORDERED_LIST"]="UNORDERED_LIST",e[t[7]="NON_EMPTY_DEFAULT"]="NON_EMPTY_DEFAULT",e[t[8]="IDENTIFIER"]="IDENTIFIER",e),i.ResourceDescriptor=(v.prototype.type="",v.prototype.pattern=a.emptyArray,v.prototype.nameField="",v.prototype.history=0,v.prototype.plural="",v.prototype.singular="",v.prototype.style=a.emptyArray,v.fromObject=function(e){if(e instanceof s.google.api.ResourceDescriptor)return e;var t=new s.google.api.ResourceDescriptor;if(null!=e.type&&(t.type=String(e.type)),e.pattern){if(!Array.isArray(e.pattern))throw TypeError(".google.api.ResourceDescriptor.pattern: array expected");t.pattern=[];for(var o=0;o<e.pattern.length;++o)t.pattern[o]=String(e.pattern[o])}switch(null!=e.nameField&&(t.nameField=String(e.nameField)),e.history){default:"number"==typeof e.history&&(t.history=e.history);break;case"HISTORY_UNSPECIFIED":case 0:t.history=0;break;case"ORIGINALLY_SINGLE_PATTERN":case 1:t.history=1;break;case"FUTURE_MULTI_PATTERN":case 2:t.history=2}if(null!=e.plural&&(t.plural=String(e.plural)),null!=e.singular&&(t.singular=String(e.singular)),e.style){if(!Array.isArray(e.style))throw TypeError(".google.api.ResourceDescriptor.style: array expected");t.style=[];for(o=0;o<e.style.length;++o)switch(e.style[o]){default:if("number"==typeof e.style[o]){t.style[o]=e.style[o];break}case"STYLE_UNSPECIFIED":case 0:t.style[o]=0;break;case"DECLARATIVE_FRIENDLY":case 1:t.style[o]=1}}return t},v.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.pattern=[],o.style=[]),t.defaults&&(o.type="",o.nameField="",o.history=t.enums===String?"HISTORY_UNSPECIFIED":0,o.plural="",o.singular=""),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),e.pattern&&e.pattern.length){o.pattern=[];for(var r=0;r<e.pattern.length;++r)o.pattern[r]=e.pattern[r]}if(null!=e.nameField&&e.hasOwnProperty("nameField")&&(o.nameField=e.nameField),null!=e.history&&e.hasOwnProperty("history")&&(o.history=t.enums!==String||void 0===s.google.api.ResourceDescriptor.History[e.history]?e.history:s.google.api.ResourceDescriptor.History[e.history]),null!=e.plural&&e.hasOwnProperty("plural")&&(o.plural=e.plural),null!=e.singular&&e.hasOwnProperty("singular")&&(o.singular=e.singular),e.style&&e.style.length){o.style=[];for(r=0;r<e.style.length;++r)o.style[r]=t.enums!==String||void 0===s.google.api.ResourceDescriptor.Style[e.style[r]]?e.style[r]:s.google.api.ResourceDescriptor.Style[e.style[r]]}return o},v.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},v.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ResourceDescriptor"},v.History=(t={},(e=Object.create(t))[t[0]="HISTORY_UNSPECIFIED"]="HISTORY_UNSPECIFIED",e[t[1]="ORIGINALLY_SINGLE_PATTERN"]="ORIGINALLY_SINGLE_PATTERN",e[t[2]="FUTURE_MULTI_PATTERN"]="FUTURE_MULTI_PATTERN",e),v.Style=(t={},(e=Object.create(t))[t[0]="STYLE_UNSPECIFIED"]="STYLE_UNSPECIFIED",e[t[1]="DECLARATIVE_FRIENDLY"]="DECLARATIVE_FRIENDLY",e),v),i.ResourceReference=(_e.prototype.type="",_e.prototype.childType="",_e.fromObject=function(e){var t;return e instanceof s.google.api.ResourceReference?e:(t=new s.google.api.ResourceReference,null!=e.type&&(t.type=String(e.type)),null!=e.childType&&(t.childType=String(e.childType)),t)},_e.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.type="",o.childType=""),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),null!=e.childType&&e.hasOwnProperty("childType")&&(o.childType=e.childType),o},_e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_e.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ResourceReference"},_e),i.Http=(Le.prototype.rules=a.emptyArray,Le.prototype.fullyDecodeReservedExpansion=!1,Le.fromObject=function(e){if(e instanceof s.google.api.Http)return e;var t=new s.google.api.Http;if(e.rules){if(!Array.isArray(e.rules))throw TypeError(".google.api.Http.rules: array expected");t.rules=[];for(var o=0;o<e.rules.length;++o){if("object"!=typeof e.rules[o])throw TypeError(".google.api.Http.rules: object expected");t.rules[o]=s.google.api.HttpRule.fromObject(e.rules[o])}}return null!=e.fullyDecodeReservedExpansion&&(t.fullyDecodeReservedExpansion=Boolean(e.fullyDecodeReservedExpansion)),t},Le.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.rules=[]),t.defaults&&(o.fullyDecodeReservedExpansion=!1),e.rules&&e.rules.length){o.rules=[];for(var r=0;r<e.rules.length;++r)o.rules[r]=s.google.api.HttpRule.toObject(e.rules[r],t)}return null!=e.fullyDecodeReservedExpansion&&e.hasOwnProperty("fullyDecodeReservedExpansion")&&(o.fullyDecodeReservedExpansion=e.fullyDecodeReservedExpansion),o},Le.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Le.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.Http"},Le),i.HttpRule=(E.prototype.selector="",E.prototype.get=null,E.prototype.put=null,E.prototype.post=null,E.prototype.delete=null,E.prototype.patch=null,E.prototype.custom=null,E.prototype.body="",E.prototype.responseBody="",E.prototype.additionalBindings=a.emptyArray,Object.defineProperty(E.prototype,"pattern",{get:a.oneOfGetter(t=["get","put","post","delete","patch","custom"]),set:a.oneOfSetter(t)}),E.fromObject=function(e){if(e instanceof s.google.api.HttpRule)return e;var t=new s.google.api.HttpRule;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.get&&(t.get=String(e.get)),null!=e.put&&(t.put=String(e.put)),null!=e.post&&(t.post=String(e.post)),null!=e.delete&&(t.delete=String(e.delete)),null!=e.patch&&(t.patch=String(e.patch)),null!=e.custom){if("object"!=typeof e.custom)throw TypeError(".google.api.HttpRule.custom: object expected");t.custom=s.google.api.CustomHttpPattern.fromObject(e.custom)}if(null!=e.body&&(t.body=String(e.body)),null!=e.responseBody&&(t.responseBody=String(e.responseBody)),e.additionalBindings){if(!Array.isArray(e.additionalBindings))throw TypeError(".google.api.HttpRule.additionalBindings: array expected");t.additionalBindings=[];for(var o=0;o<e.additionalBindings.length;++o){if("object"!=typeof e.additionalBindings[o])throw TypeError(".google.api.HttpRule.additionalBindings: object expected");t.additionalBindings[o]=s.google.api.HttpRule.fromObject(e.additionalBindings[o])}}return t},E.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.additionalBindings=[]),t.defaults&&(o.selector="",o.body="",o.responseBody=""),null!=e.selector&&e.hasOwnProperty("selector")&&(o.selector=e.selector),null!=e.get&&e.hasOwnProperty("get")&&(o.get=e.get,t.oneofs)&&(o.pattern="get"),null!=e.put&&e.hasOwnProperty("put")&&(o.put=e.put,t.oneofs)&&(o.pattern="put"),null!=e.post&&e.hasOwnProperty("post")&&(o.post=e.post,t.oneofs)&&(o.pattern="post"),null!=e.delete&&e.hasOwnProperty("delete")&&(o.delete=e.delete,t.oneofs)&&(o.pattern="delete"),null!=e.patch&&e.hasOwnProperty("patch")&&(o.patch=e.patch,t.oneofs)&&(o.pattern="patch"),null!=e.body&&e.hasOwnProperty("body")&&(o.body=e.body),null!=e.custom&&e.hasOwnProperty("custom")&&(o.custom=s.google.api.CustomHttpPattern.toObject(e.custom,t),t.oneofs)&&(o.pattern="custom"),e.additionalBindings&&e.additionalBindings.length){o.additionalBindings=[];for(var r=0;r<e.additionalBindings.length;++r)o.additionalBindings[r]=s.google.api.HttpRule.toObject(e.additionalBindings[r],t)}return null!=e.responseBody&&e.hasOwnProperty("responseBody")&&(o.responseBody=e.responseBody),o},E.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},E.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.HttpRule"},E),i.CustomHttpPattern=(Fe.prototype.kind="",Fe.prototype.path="",Fe.fromObject=function(e){var t;return e instanceof s.google.api.CustomHttpPattern?e:(t=new s.google.api.CustomHttpPattern,null!=e.kind&&(t.kind=String(e.kind)),null!=e.path&&(t.path=String(e.path)),t)},Fe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.kind="",o.path=""),null!=e.kind&&e.hasOwnProperty("kind")&&(o.kind=e.kind),null!=e.path&&e.hasOwnProperty("path")&&(o.path=e.path),o},Fe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Fe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CustomHttpPattern"},Fe),i.CommonLanguageSettings=(Ue.prototype.referenceDocsUri="",Ue.prototype.destinations=a.emptyArray,Ue.prototype.selectiveGapicGeneration=null,Ue.fromObject=function(e){if(e instanceof s.google.api.CommonLanguageSettings)return e;var t=new s.google.api.CommonLanguageSettings;if(null!=e.referenceDocsUri&&(t.referenceDocsUri=String(e.referenceDocsUri)),e.destinations){if(!Array.isArray(e.destinations))throw TypeError(".google.api.CommonLanguageSettings.destinations: array expected");t.destinations=[];for(var o=0;o<e.destinations.length;++o)switch(e.destinations[o]){default:if("number"==typeof e.destinations[o]){t.destinations[o]=e.destinations[o];break}case"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED":case 0:t.destinations[o]=0;break;case"GITHUB":case 10:t.destinations[o]=10;break;case"PACKAGE_MANAGER":case 20:t.destinations[o]=20}}if(null!=e.selectiveGapicGeneration){if("object"!=typeof e.selectiveGapicGeneration)throw TypeError(".google.api.CommonLanguageSettings.selectiveGapicGeneration: object expected");t.selectiveGapicGeneration=s.google.api.SelectiveGapicGeneration.fromObject(e.selectiveGapicGeneration)}return t},Ue.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.destinations=[]),t.defaults&&(o.referenceDocsUri="",o.selectiveGapicGeneration=null),null!=e.referenceDocsUri&&e.hasOwnProperty("referenceDocsUri")&&(o.referenceDocsUri=e.referenceDocsUri),e.destinations&&e.destinations.length){o.destinations=[];for(var r=0;r<e.destinations.length;++r)o.destinations[r]=t.enums!==String||void 0===s.google.api.ClientLibraryDestination[e.destinations[r]]?e.destinations[r]:s.google.api.ClientLibraryDestination[e.destinations[r]]}return null!=e.selectiveGapicGeneration&&e.hasOwnProperty("selectiveGapicGeneration")&&(o.selectiveGapicGeneration=s.google.api.SelectiveGapicGeneration.toObject(e.selectiveGapicGeneration,t)),o},Ue.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ue.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CommonLanguageSettings"},Ue),i.ClientLibrarySettings=(T.prototype.version="",T.prototype.launchStage=0,T.prototype.restNumericEnums=!1,T.prototype.javaSettings=null,T.prototype.cppSettings=null,T.prototype.phpSettings=null,T.prototype.pythonSettings=null,T.prototype.nodeSettings=null,T.prototype.dotnetSettings=null,T.prototype.rubySettings=null,T.prototype.goSettings=null,T.fromObject=function(e){if(e instanceof s.google.api.ClientLibrarySettings)return e;var t=new s.google.api.ClientLibrarySettings;switch(null!=e.version&&(t.version=String(e.version)),e.launchStage){default:"number"==typeof e.launchStage&&(t.launchStage=e.launchStage);break;case"LAUNCH_STAGE_UNSPECIFIED":case 0:t.launchStage=0;break;case"UNIMPLEMENTED":case 6:t.launchStage=6;break;case"PRELAUNCH":case 7:t.launchStage=7;break;case"EARLY_ACCESS":case 1:t.launchStage=1;break;case"ALPHA":case 2:t.launchStage=2;break;case"BETA":case 3:t.launchStage=3;break;case"GA":case 4:t.launchStage=4;break;case"DEPRECATED":case 5:t.launchStage=5}if(null!=e.restNumericEnums&&(t.restNumericEnums=Boolean(e.restNumericEnums)),null!=e.javaSettings){if("object"!=typeof e.javaSettings)throw TypeError(".google.api.ClientLibrarySettings.javaSettings: object expected");t.javaSettings=s.google.api.JavaSettings.fromObject(e.javaSettings)}if(null!=e.cppSettings){if("object"!=typeof e.cppSettings)throw TypeError(".google.api.ClientLibrarySettings.cppSettings: object expected");t.cppSettings=s.google.api.CppSettings.fromObject(e.cppSettings)}if(null!=e.phpSettings){if("object"!=typeof e.phpSettings)throw TypeError(".google.api.ClientLibrarySettings.phpSettings: object expected");t.phpSettings=s.google.api.PhpSettings.fromObject(e.phpSettings)}if(null!=e.pythonSettings){if("object"!=typeof e.pythonSettings)throw TypeError(".google.api.ClientLibrarySettings.pythonSettings: object expected");t.pythonSettings=s.google.api.PythonSettings.fromObject(e.pythonSettings)}if(null!=e.nodeSettings){if("object"!=typeof e.nodeSettings)throw TypeError(".google.api.ClientLibrarySettings.nodeSettings: object expected");t.nodeSettings=s.google.api.NodeSettings.fromObject(e.nodeSettings)}if(null!=e.dotnetSettings){if("object"!=typeof e.dotnetSettings)throw TypeError(".google.api.ClientLibrarySettings.dotnetSettings: object expected");t.dotnetSettings=s.google.api.DotnetSettings.fromObject(e.dotnetSettings)}if(null!=e.rubySettings){if("object"!=typeof e.rubySettings)throw TypeError(".google.api.ClientLibrarySettings.rubySettings: object expected");t.rubySettings=s.google.api.RubySettings.fromObject(e.rubySettings)}if(null!=e.goSettings){if("object"!=typeof e.goSettings)throw TypeError(".google.api.ClientLibrarySettings.goSettings: object expected");t.goSettings=s.google.api.GoSettings.fromObject(e.goSettings)}return t},T.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.version="",o.launchStage=t.enums===String?"LAUNCH_STAGE_UNSPECIFIED":0,o.restNumericEnums=!1,o.javaSettings=null,o.cppSettings=null,o.phpSettings=null,o.pythonSettings=null,o.nodeSettings=null,o.dotnetSettings=null,o.rubySettings=null,o.goSettings=null),null!=e.version&&e.hasOwnProperty("version")&&(o.version=e.version),null!=e.launchStage&&e.hasOwnProperty("launchStage")&&(o.launchStage=t.enums!==String||void 0===s.google.api.LaunchStage[e.launchStage]?e.launchStage:s.google.api.LaunchStage[e.launchStage]),null!=e.restNumericEnums&&e.hasOwnProperty("restNumericEnums")&&(o.restNumericEnums=e.restNumericEnums),null!=e.javaSettings&&e.hasOwnProperty("javaSettings")&&(o.javaSettings=s.google.api.JavaSettings.toObject(e.javaSettings,t)),null!=e.cppSettings&&e.hasOwnProperty("cppSettings")&&(o.cppSettings=s.google.api.CppSettings.toObject(e.cppSettings,t)),null!=e.phpSettings&&e.hasOwnProperty("phpSettings")&&(o.phpSettings=s.google.api.PhpSettings.toObject(e.phpSettings,t)),null!=e.pythonSettings&&e.hasOwnProperty("pythonSettings")&&(o.pythonSettings=s.google.api.PythonSettings.toObject(e.pythonSettings,t)),null!=e.nodeSettings&&e.hasOwnProperty("nodeSettings")&&(o.nodeSettings=s.google.api.NodeSettings.toObject(e.nodeSettings,t)),null!=e.dotnetSettings&&e.hasOwnProperty("dotnetSettings")&&(o.dotnetSettings=s.google.api.DotnetSettings.toObject(e.dotnetSettings,t)),null!=e.rubySettings&&e.hasOwnProperty("rubySettings")&&(o.rubySettings=s.google.api.RubySettings.toObject(e.rubySettings,t)),null!=e.goSettings&&e.hasOwnProperty("goSettings")&&(o.goSettings=s.google.api.GoSettings.toObject(e.goSettings,t)),o},T.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},T.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ClientLibrarySettings"},T),i.Publishing=(I.prototype.methodSettings=a.emptyArray,I.prototype.newIssueUri="",I.prototype.documentationUri="",I.prototype.apiShortName="",I.prototype.githubLabel="",I.prototype.codeownerGithubTeams=a.emptyArray,I.prototype.docTagPrefix="",I.prototype.organization=0,I.prototype.librarySettings=a.emptyArray,I.prototype.protoReferenceDocumentationUri="",I.prototype.restReferenceDocumentationUri="",I.fromObject=function(e){if(e instanceof s.google.api.Publishing)return e;var t=new s.google.api.Publishing;if(e.methodSettings){if(!Array.isArray(e.methodSettings))throw TypeError(".google.api.Publishing.methodSettings: array expected");t.methodSettings=[];for(var o=0;o<e.methodSettings.length;++o){if("object"!=typeof e.methodSettings[o])throw TypeError(".google.api.Publishing.methodSettings: object expected");t.methodSettings[o]=s.google.api.MethodSettings.fromObject(e.methodSettings[o])}}if(null!=e.newIssueUri&&(t.newIssueUri=String(e.newIssueUri)),null!=e.documentationUri&&(t.documentationUri=String(e.documentationUri)),null!=e.apiShortName&&(t.apiShortName=String(e.apiShortName)),null!=e.githubLabel&&(t.githubLabel=String(e.githubLabel)),e.codeownerGithubTeams){if(!Array.isArray(e.codeownerGithubTeams))throw TypeError(".google.api.Publishing.codeownerGithubTeams: array expected");t.codeownerGithubTeams=[];for(o=0;o<e.codeownerGithubTeams.length;++o)t.codeownerGithubTeams[o]=String(e.codeownerGithubTeams[o])}switch(null!=e.docTagPrefix&&(t.docTagPrefix=String(e.docTagPrefix)),e.organization){default:"number"==typeof e.organization&&(t.organization=e.organization);break;case"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":case 0:t.organization=0;break;case"CLOUD":case 1:t.organization=1;break;case"ADS":case 2:t.organization=2;break;case"PHOTOS":case 3:t.organization=3;break;case"STREET_VIEW":case 4:t.organization=4;break;case"SHOPPING":case 5:t.organization=5;break;case"GEO":case 6:t.organization=6;break;case"GENERATIVE_AI":case 7:t.organization=7}if(e.librarySettings){if(!Array.isArray(e.librarySettings))throw TypeError(".google.api.Publishing.librarySettings: array expected");t.librarySettings=[];for(o=0;o<e.librarySettings.length;++o){if("object"!=typeof e.librarySettings[o])throw TypeError(".google.api.Publishing.librarySettings: object expected");t.librarySettings[o]=s.google.api.ClientLibrarySettings.fromObject(e.librarySettings[o])}}return null!=e.protoReferenceDocumentationUri&&(t.protoReferenceDocumentationUri=String(e.protoReferenceDocumentationUri)),null!=e.restReferenceDocumentationUri&&(t.restReferenceDocumentationUri=String(e.restReferenceDocumentationUri)),t},I.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.methodSettings=[],o.codeownerGithubTeams=[],o.librarySettings=[]),t.defaults&&(o.newIssueUri="",o.documentationUri="",o.apiShortName="",o.githubLabel="",o.docTagPrefix="",o.organization=t.enums===String?"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":0,o.protoReferenceDocumentationUri="",o.restReferenceDocumentationUri=""),e.methodSettings&&e.methodSettings.length){o.methodSettings=[];for(var r=0;r<e.methodSettings.length;++r)o.methodSettings[r]=s.google.api.MethodSettings.toObject(e.methodSettings[r],t)}if(null!=e.newIssueUri&&e.hasOwnProperty("newIssueUri")&&(o.newIssueUri=e.newIssueUri),null!=e.documentationUri&&e.hasOwnProperty("documentationUri")&&(o.documentationUri=e.documentationUri),null!=e.apiShortName&&e.hasOwnProperty("apiShortName")&&(o.apiShortName=e.apiShortName),null!=e.githubLabel&&e.hasOwnProperty("githubLabel")&&(o.githubLabel=e.githubLabel),e.codeownerGithubTeams&&e.codeownerGithubTeams.length){o.codeownerGithubTeams=[];for(r=0;r<e.codeownerGithubTeams.length;++r)o.codeownerGithubTeams[r]=e.codeownerGithubTeams[r]}if(null!=e.docTagPrefix&&e.hasOwnProperty("docTagPrefix")&&(o.docTagPrefix=e.docTagPrefix),null!=e.organization&&e.hasOwnProperty("organization")&&(o.organization=t.enums!==String||void 0===s.google.api.ClientLibraryOrganization[e.organization]?e.organization:s.google.api.ClientLibraryOrganization[e.organization]),e.librarySettings&&e.librarySettings.length){o.librarySettings=[];for(r=0;r<e.librarySettings.length;++r)o.librarySettings[r]=s.google.api.ClientLibrarySettings.toObject(e.librarySettings[r],t)}return null!=e.protoReferenceDocumentationUri&&e.hasOwnProperty("protoReferenceDocumentationUri")&&(o.protoReferenceDocumentationUri=e.protoReferenceDocumentationUri),null!=e.restReferenceDocumentationUri&&e.hasOwnProperty("restReferenceDocumentationUri")&&(o.restReferenceDocumentationUri=e.restReferenceDocumentationUri),o},I.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},I.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.Publishing"},I),i.JavaSettings=(Be.prototype.libraryPackage="",Be.prototype.serviceClassNames=a.emptyObject,Be.prototype.common=null,Be.fromObject=function(e){if(e instanceof s.google.api.JavaSettings)return e;var t=new s.google.api.JavaSettings;if(null!=e.libraryPackage&&(t.libraryPackage=String(e.libraryPackage)),e.serviceClassNames){if("object"!=typeof e.serviceClassNames)throw TypeError(".google.api.JavaSettings.serviceClassNames: object expected");t.serviceClassNames={};for(var o=Object.keys(e.serviceClassNames),r=0;r<o.length;++r)t.serviceClassNames[o[r]]=String(e.serviceClassNames[o[r]])}if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.JavaSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}return t},Be.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.serviceClassNames={}),t.defaults&&(r.libraryPackage="",r.common=null),null!=e.libraryPackage&&e.hasOwnProperty("libraryPackage")&&(r.libraryPackage=e.libraryPackage),e.serviceClassNames&&(o=Object.keys(e.serviceClassNames)).length){r.serviceClassNames={};for(var n=0;n<o.length;++n)r.serviceClassNames[o[n]]=e.serviceClassNames[o[n]]}return null!=e.common&&e.hasOwnProperty("common")&&(r.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),r},Be.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Be.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.JavaSettings"},Be),i.CppSettings=(Me.prototype.common=null,Me.fromObject=function(e){if(e instanceof s.google.api.CppSettings)return e;var t=new s.google.api.CppSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.CppSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}return t},Me.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),o},Me.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Me.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CppSettings"},Me),i.PhpSettings=(Ve.prototype.common=null,Ve.fromObject=function(e){if(e instanceof s.google.api.PhpSettings)return e;var t=new s.google.api.PhpSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.PhpSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}return t},Ve.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),o},Ve.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ve.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PhpSettings"},Ve),i.PythonSettings=(Ge.prototype.common=null,Ge.prototype.experimentalFeatures=null,Ge.fromObject=function(e){if(e instanceof s.google.api.PythonSettings)return e;var t=new s.google.api.PythonSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.PythonSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}if(null!=e.experimentalFeatures){if("object"!=typeof e.experimentalFeatures)throw TypeError(".google.api.PythonSettings.experimentalFeatures: object expected");t.experimentalFeatures=s.google.api.PythonSettings.ExperimentalFeatures.fromObject(e.experimentalFeatures)}return t},Ge.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null,o.experimentalFeatures=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),null!=e.experimentalFeatures&&e.hasOwnProperty("experimentalFeatures")&&(o.experimentalFeatures=s.google.api.PythonSettings.ExperimentalFeatures.toObject(e.experimentalFeatures,t)),o},Ge.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ge.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PythonSettings"},Ge.ExperimentalFeatures=(Je.prototype.restAsyncIoEnabled=!1,Je.prototype.protobufPythonicTypesEnabled=!1,Je.fromObject=function(e){var t;return e instanceof s.google.api.PythonSettings.ExperimentalFeatures?e:(t=new s.google.api.PythonSettings.ExperimentalFeatures,null!=e.restAsyncIoEnabled&&(t.restAsyncIoEnabled=Boolean(e.restAsyncIoEnabled)),null!=e.protobufPythonicTypesEnabled&&(t.protobufPythonicTypesEnabled=Boolean(e.protobufPythonicTypesEnabled)),t)},Je.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.restAsyncIoEnabled=!1,o.protobufPythonicTypesEnabled=!1),null!=e.restAsyncIoEnabled&&e.hasOwnProperty("restAsyncIoEnabled")&&(o.restAsyncIoEnabled=e.restAsyncIoEnabled),null!=e.protobufPythonicTypesEnabled&&e.hasOwnProperty("protobufPythonicTypesEnabled")&&(o.protobufPythonicTypesEnabled=e.protobufPythonicTypesEnabled),o},Je.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Je.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PythonSettings.ExperimentalFeatures"},Je),Ge),i.NodeSettings=(Ye.prototype.common=null,Ye.fromObject=function(e){if(e instanceof s.google.api.NodeSettings)return e;var t=new s.google.api.NodeSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.NodeSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}return t},Ye.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),o},Ye.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ye.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.NodeSettings"},Ye),i.DotnetSettings=(j.prototype.common=null,j.prototype.renamedServices=a.emptyObject,j.prototype.renamedResources=a.emptyObject,j.prototype.ignoredResources=a.emptyArray,j.prototype.forcedNamespaceAliases=a.emptyArray,j.prototype.handwrittenSignatures=a.emptyArray,j.fromObject=function(e){if(e instanceof s.google.api.DotnetSettings)return e;var t=new s.google.api.DotnetSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.DotnetSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}if(e.renamedServices){if("object"!=typeof e.renamedServices)throw TypeError(".google.api.DotnetSettings.renamedServices: object expected");t.renamedServices={};for(var o=Object.keys(e.renamedServices),r=0;r<o.length;++r)t.renamedServices[o[r]]=String(e.renamedServices[o[r]])}if(e.renamedResources){if("object"!=typeof e.renamedResources)throw TypeError(".google.api.DotnetSettings.renamedResources: object expected");t.renamedResources={};for(o=Object.keys(e.renamedResources),r=0;r<o.length;++r)t.renamedResources[o[r]]=String(e.renamedResources[o[r]])}if(e.ignoredResources){if(!Array.isArray(e.ignoredResources))throw TypeError(".google.api.DotnetSettings.ignoredResources: array expected");t.ignoredResources=[];for(r=0;r<e.ignoredResources.length;++r)t.ignoredResources[r]=String(e.ignoredResources[r])}if(e.forcedNamespaceAliases){if(!Array.isArray(e.forcedNamespaceAliases))throw TypeError(".google.api.DotnetSettings.forcedNamespaceAliases: array expected");t.forcedNamespaceAliases=[];for(r=0;r<e.forcedNamespaceAliases.length;++r)t.forcedNamespaceAliases[r]=String(e.forcedNamespaceAliases[r])}if(e.handwrittenSignatures){if(!Array.isArray(e.handwrittenSignatures))throw TypeError(".google.api.DotnetSettings.handwrittenSignatures: array expected");t.handwrittenSignatures=[];for(r=0;r<e.handwrittenSignatures.length;++r)t.handwrittenSignatures[r]=String(e.handwrittenSignatures[r])}return t},j.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.ignoredResources=[],r.forcedNamespaceAliases=[],r.handwrittenSignatures=[]),(t.objects||t.defaults)&&(r.renamedServices={},r.renamedResources={}),t.defaults&&(r.common=null),null!=e.common&&e.hasOwnProperty("common")&&(r.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),e.renamedServices&&(o=Object.keys(e.renamedServices)).length){r.renamedServices={};for(var n=0;n<o.length;++n)r.renamedServices[o[n]]=e.renamedServices[o[n]]}if(e.renamedResources&&(o=Object.keys(e.renamedResources)).length){r.renamedResources={};for(n=0;n<o.length;++n)r.renamedResources[o[n]]=e.renamedResources[o[n]]}if(e.ignoredResources&&e.ignoredResources.length){r.ignoredResources=[];for(n=0;n<e.ignoredResources.length;++n)r.ignoredResources[n]=e.ignoredResources[n]}if(e.forcedNamespaceAliases&&e.forcedNamespaceAliases.length){r.forcedNamespaceAliases=[];for(n=0;n<e.forcedNamespaceAliases.length;++n)r.forcedNamespaceAliases[n]=e.forcedNamespaceAliases[n]}if(e.handwrittenSignatures&&e.handwrittenSignatures.length){r.handwrittenSignatures=[];for(n=0;n<e.handwrittenSignatures.length;++n)r.handwrittenSignatures[n]=e.handwrittenSignatures[n]}return r},j.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},j.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.DotnetSettings"},j),i.RubySettings=(qe.prototype.common=null,qe.fromObject=function(e){if(e instanceof s.google.api.RubySettings)return e;var t=new s.google.api.RubySettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.RubySettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}return t},qe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),o},qe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},qe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.RubySettings"},qe),i.GoSettings=(We.prototype.common=null,We.prototype.renamedServices=a.emptyObject,We.fromObject=function(e){if(e instanceof s.google.api.GoSettings)return e;var t=new s.google.api.GoSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.GoSettings.common: object expected");t.common=s.google.api.CommonLanguageSettings.fromObject(e.common)}if(e.renamedServices){if("object"!=typeof e.renamedServices)throw TypeError(".google.api.GoSettings.renamedServices: object expected");t.renamedServices={};for(var o=Object.keys(e.renamedServices),r=0;r<o.length;++r)t.renamedServices[o[r]]=String(e.renamedServices[o[r]])}return t},We.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.renamedServices={}),t.defaults&&(r.common=null),null!=e.common&&e.hasOwnProperty("common")&&(r.common=s.google.api.CommonLanguageSettings.toObject(e.common,t)),e.renamedServices&&(o=Object.keys(e.renamedServices)).length){r.renamedServices={};for(var n=0;n<o.length;++n)r.renamedServices[o[n]]=e.renamedServices[o[n]]}return r},We.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},We.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.GoSettings"},We),i.MethodSettings=(ze.prototype.selector="",ze.prototype.longRunning=null,ze.prototype.autoPopulatedFields=a.emptyArray,ze.fromObject=function(e){if(e instanceof s.google.api.MethodSettings)return e;var t=new s.google.api.MethodSettings;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.longRunning){if("object"!=typeof e.longRunning)throw TypeError(".google.api.MethodSettings.longRunning: object expected");t.longRunning=s.google.api.MethodSettings.LongRunning.fromObject(e.longRunning)}if(e.autoPopulatedFields){if(!Array.isArray(e.autoPopulatedFields))throw TypeError(".google.api.MethodSettings.autoPopulatedFields: array expected");t.autoPopulatedFields=[];for(var o=0;o<e.autoPopulatedFields.length;++o)t.autoPopulatedFields[o]=String(e.autoPopulatedFields[o])}return t},ze.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.autoPopulatedFields=[]),t.defaults&&(o.selector="",o.longRunning=null),null!=e.selector&&e.hasOwnProperty("selector")&&(o.selector=e.selector),null!=e.longRunning&&e.hasOwnProperty("longRunning")&&(o.longRunning=s.google.api.MethodSettings.LongRunning.toObject(e.longRunning,t)),e.autoPopulatedFields&&e.autoPopulatedFields.length){o.autoPopulatedFields=[];for(var r=0;r<e.autoPopulatedFields.length;++r)o.autoPopulatedFields[r]=e.autoPopulatedFields[r]}return o},ze.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ze.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.MethodSettings"},ze.LongRunning=(He.prototype.initialPollDelay=null,He.prototype.pollDelayMultiplier=0,He.prototype.maxPollDelay=null,He.prototype.totalPollTimeout=null,He.fromObject=function(e){if(e instanceof s.google.api.MethodSettings.LongRunning)return e;var t=new s.google.api.MethodSettings.LongRunning;if(null!=e.initialPollDelay){if("object"!=typeof e.initialPollDelay)throw TypeError(".google.api.MethodSettings.LongRunning.initialPollDelay: object expected");t.initialPollDelay=s.google.protobuf.Duration.fromObject(e.initialPollDelay)}if(null!=e.pollDelayMultiplier&&(t.pollDelayMultiplier=Number(e.pollDelayMultiplier)),null!=e.maxPollDelay){if("object"!=typeof e.maxPollDelay)throw TypeError(".google.api.MethodSettings.LongRunning.maxPollDelay: object expected");t.maxPollDelay=s.google.protobuf.Duration.fromObject(e.maxPollDelay)}if(null!=e.totalPollTimeout){if("object"!=typeof e.totalPollTimeout)throw TypeError(".google.api.MethodSettings.LongRunning.totalPollTimeout: object expected");t.totalPollTimeout=s.google.protobuf.Duration.fromObject(e.totalPollTimeout)}return t},He.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.initialPollDelay=null,o.pollDelayMultiplier=0,o.maxPollDelay=null,o.totalPollTimeout=null),null!=e.initialPollDelay&&e.hasOwnProperty("initialPollDelay")&&(o.initialPollDelay=s.google.protobuf.Duration.toObject(e.initialPollDelay,t)),null!=e.pollDelayMultiplier&&e.hasOwnProperty("pollDelayMultiplier")&&(o.pollDelayMultiplier=t.json&&!isFinite(e.pollDelayMultiplier)?String(e.pollDelayMultiplier):e.pollDelayMultiplier),null!=e.maxPollDelay&&e.hasOwnProperty("maxPollDelay")&&(o.maxPollDelay=s.google.protobuf.Duration.toObject(e.maxPollDelay,t)),null!=e.totalPollTimeout&&e.hasOwnProperty("totalPollTimeout")&&(o.totalPollTimeout=s.google.protobuf.Duration.toObject(e.totalPollTimeout,t)),o},He.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},He.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.MethodSettings.LongRunning"},He),ze),i.ClientLibraryOrganization=(e={},(t=Object.create(e))[e[0]="CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED"]="CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED",t[e[1]="CLOUD"]="CLOUD",t[e[2]="ADS"]="ADS",t[e[3]="PHOTOS"]="PHOTOS",t[e[4]="STREET_VIEW"]="STREET_VIEW",t[e[5]="SHOPPING"]="SHOPPING",t[e[6]="GEO"]="GEO",t[e[7]="GENERATIVE_AI"]="GENERATIVE_AI",t),i.ClientLibraryDestination=(e={},(t=Object.create(e))[e[0]="CLIENT_LIBRARY_DESTINATION_UNSPECIFIED"]="CLIENT_LIBRARY_DESTINATION_UNSPECIFIED",t[e[10]="GITHUB"]="GITHUB",t[e[20]="PACKAGE_MANAGER"]="PACKAGE_MANAGER",t),i.SelectiveGapicGeneration=(Ke.prototype.methods=a.emptyArray,Ke.prototype.generateOmittedAsInternal=!1,Ke.fromObject=function(e){if(e instanceof s.google.api.SelectiveGapicGeneration)return e;var t=new s.google.api.SelectiveGapicGeneration;if(e.methods){if(!Array.isArray(e.methods))throw TypeError(".google.api.SelectiveGapicGeneration.methods: array expected");t.methods=[];for(var o=0;o<e.methods.length;++o)t.methods[o]=String(e.methods[o])}return null!=e.generateOmittedAsInternal&&(t.generateOmittedAsInternal=Boolean(e.generateOmittedAsInternal)),t},Ke.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.methods=[]),t.defaults&&(o.generateOmittedAsInternal=!1),e.methods&&e.methods.length){o.methods=[];for(var r=0;r<e.methods.length;++r)o.methods[r]=e.methods[r]}return null!=e.generateOmittedAsInternal&&e.hasOwnProperty("generateOmittedAsInternal")&&(o.generateOmittedAsInternal=e.generateOmittedAsInternal),o},Ke.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ke.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.SelectiveGapicGeneration"},Ke),i.LaunchStage=(e={},(t=Object.create(e))[e[0]="LAUNCH_STAGE_UNSPECIFIED"]="LAUNCH_STAGE_UNSPECIFIED",t[e[6]="UNIMPLEMENTED"]="UNIMPLEMENTED",t[e[7]="PRELAUNCH"]="PRELAUNCH",t[e[1]="EARLY_ACCESS"]="EARLY_ACCESS",t[e[2]="ALPHA"]="ALPHA",t[e[3]="BETA"]="BETA",t[e[4]="GA"]="GA",t[e[5]="DEPRECATED"]="DEPRECATED",t),i),L.protobuf=((o={}).FileDescriptorSet=(Xe.prototype.file=a.emptyArray,Xe.fromObject=function(e){if(e instanceof s.google.protobuf.FileDescriptorSet)return e;var t=new s.google.protobuf.FileDescriptorSet;if(e.file){if(!Array.isArray(e.file))throw TypeError(".google.protobuf.FileDescriptorSet.file: array expected");t.file=[];for(var o=0;o<e.file.length;++o){if("object"!=typeof e.file[o])throw TypeError(".google.protobuf.FileDescriptorSet.file: object expected");t.file[o]=s.google.protobuf.FileDescriptorProto.fromObject(e.file[o])}}return t},Xe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.file=[]),e.file&&e.file.length){o.file=[];for(var r=0;r<e.file.length;++r)o.file[r]=s.google.protobuf.FileDescriptorProto.toObject(e.file[r],t)}return o},Xe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Xe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileDescriptorSet"},Xe),o.Edition=(n={},(e=Object.create(n))[n[0]="EDITION_UNKNOWN"]="EDITION_UNKNOWN",e[n[998]="EDITION_PROTO2"]="EDITION_PROTO2",e[n[999]="EDITION_PROTO3"]="EDITION_PROTO3",e[n[1e3]="EDITION_2023"]="EDITION_2023",e[n[1001]="EDITION_2024"]="EDITION_2024",e[n[1]="EDITION_1_TEST_ONLY"]="EDITION_1_TEST_ONLY",e[n[2]="EDITION_2_TEST_ONLY"]="EDITION_2_TEST_ONLY",e[n[99997]="EDITION_99997_TEST_ONLY"]="EDITION_99997_TEST_ONLY",e[n[99998]="EDITION_99998_TEST_ONLY"]="EDITION_99998_TEST_ONLY",e[n[99999]="EDITION_99999_TEST_ONLY"]="EDITION_99999_TEST_ONLY",e[n[2147483647]="EDITION_MAX"]="EDITION_MAX",e),o.FileDescriptorProto=(N.prototype.name="",N.prototype.package="",N.prototype.dependency=a.emptyArray,N.prototype.publicDependency=a.emptyArray,N.prototype.weakDependency=a.emptyArray,N.prototype.messageType=a.emptyArray,N.prototype.enumType=a.emptyArray,N.prototype.service=a.emptyArray,N.prototype.extension=a.emptyArray,N.prototype.options=null,N.prototype.sourceCodeInfo=null,N.prototype.syntax="",N.prototype.edition=0,N.fromObject=function(e){if(e instanceof s.google.protobuf.FileDescriptorProto)return e;var t=new s.google.protobuf.FileDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.package&&(t.package=String(e.package)),e.dependency){if(!Array.isArray(e.dependency))throw TypeError(".google.protobuf.FileDescriptorProto.dependency: array expected");t.dependency=[];for(var o=0;o<e.dependency.length;++o)t.dependency[o]=String(e.dependency[o])}if(e.publicDependency){if(!Array.isArray(e.publicDependency))throw TypeError(".google.protobuf.FileDescriptorProto.publicDependency: array expected");t.publicDependency=[];for(o=0;o<e.publicDependency.length;++o)t.publicDependency[o]=0|e.publicDependency[o]}if(e.weakDependency){if(!Array.isArray(e.weakDependency))throw TypeError(".google.protobuf.FileDescriptorProto.weakDependency: array expected");t.weakDependency=[];for(o=0;o<e.weakDependency.length;++o)t.weakDependency[o]=0|e.weakDependency[o]}if(e.messageType){if(!Array.isArray(e.messageType))throw TypeError(".google.protobuf.FileDescriptorProto.messageType: array expected");t.messageType=[];for(o=0;o<e.messageType.length;++o){if("object"!=typeof e.messageType[o])throw TypeError(".google.protobuf.FileDescriptorProto.messageType: object expected");t.messageType[o]=s.google.protobuf.DescriptorProto.fromObject(e.messageType[o])}}if(e.enumType){if(!Array.isArray(e.enumType))throw TypeError(".google.protobuf.FileDescriptorProto.enumType: array expected");t.enumType=[];for(o=0;o<e.enumType.length;++o){if("object"!=typeof e.enumType[o])throw TypeError(".google.protobuf.FileDescriptorProto.enumType: object expected");t.enumType[o]=s.google.protobuf.EnumDescriptorProto.fromObject(e.enumType[o])}}if(e.service){if(!Array.isArray(e.service))throw TypeError(".google.protobuf.FileDescriptorProto.service: array expected");t.service=[];for(o=0;o<e.service.length;++o){if("object"!=typeof e.service[o])throw TypeError(".google.protobuf.FileDescriptorProto.service: object expected");t.service[o]=s.google.protobuf.ServiceDescriptorProto.fromObject(e.service[o])}}if(e.extension){if(!Array.isArray(e.extension))throw TypeError(".google.protobuf.FileDescriptorProto.extension: array expected");t.extension=[];for(o=0;o<e.extension.length;++o){if("object"!=typeof e.extension[o])throw TypeError(".google.protobuf.FileDescriptorProto.extension: object expected");t.extension[o]=s.google.protobuf.FieldDescriptorProto.fromObject(e.extension[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.FileDescriptorProto.options: object expected");t.options=s.google.protobuf.FileOptions.fromObject(e.options)}if(null!=e.sourceCodeInfo){if("object"!=typeof e.sourceCodeInfo)throw TypeError(".google.protobuf.FileDescriptorProto.sourceCodeInfo: object expected");t.sourceCodeInfo=s.google.protobuf.SourceCodeInfo.fromObject(e.sourceCodeInfo)}switch(null!=e.syntax&&(t.syntax=String(e.syntax)),e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}return t},N.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.dependency=[],o.messageType=[],o.enumType=[],o.service=[],o.extension=[],o.publicDependency=[],o.weakDependency=[]),t.defaults&&(o.name="",o.package="",o.options=null,o.sourceCodeInfo=null,o.syntax="",o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.package&&e.hasOwnProperty("package")&&(o.package=e.package),e.dependency&&e.dependency.length){o.dependency=[];for(var r=0;r<e.dependency.length;++r)o.dependency[r]=e.dependency[r]}if(e.messageType&&e.messageType.length){o.messageType=[];for(r=0;r<e.messageType.length;++r)o.messageType[r]=s.google.protobuf.DescriptorProto.toObject(e.messageType[r],t)}if(e.enumType&&e.enumType.length){o.enumType=[];for(r=0;r<e.enumType.length;++r)o.enumType[r]=s.google.protobuf.EnumDescriptorProto.toObject(e.enumType[r],t)}if(e.service&&e.service.length){o.service=[];for(r=0;r<e.service.length;++r)o.service[r]=s.google.protobuf.ServiceDescriptorProto.toObject(e.service[r],t)}if(e.extension&&e.extension.length){o.extension=[];for(r=0;r<e.extension.length;++r)o.extension[r]=s.google.protobuf.FieldDescriptorProto.toObject(e.extension[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.FileOptions.toObject(e.options,t)),null!=e.sourceCodeInfo&&e.hasOwnProperty("sourceCodeInfo")&&(o.sourceCodeInfo=s.google.protobuf.SourceCodeInfo.toObject(e.sourceCodeInfo,t)),e.publicDependency&&e.publicDependency.length){o.publicDependency=[];for(r=0;r<e.publicDependency.length;++r)o.publicDependency[r]=e.publicDependency[r]}if(e.weakDependency&&e.weakDependency.length){o.weakDependency=[];for(r=0;r<e.weakDependency.length;++r)o.weakDependency[r]=e.weakDependency[r]}return null!=e.syntax&&e.hasOwnProperty("syntax")&&(o.syntax=e.syntax),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===s.google.protobuf.Edition[e.edition]?e.edition:s.google.protobuf.Edition[e.edition]),o},N.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},N.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileDescriptorProto"},N),o.DescriptorProto=(P.prototype.name="",P.prototype.field=a.emptyArray,P.prototype.extension=a.emptyArray,P.prototype.nestedType=a.emptyArray,P.prototype.enumType=a.emptyArray,P.prototype.extensionRange=a.emptyArray,P.prototype.oneofDecl=a.emptyArray,P.prototype.options=null,P.prototype.reservedRange=a.emptyArray,P.prototype.reservedName=a.emptyArray,P.fromObject=function(e){if(e instanceof s.google.protobuf.DescriptorProto)return e;var t=new s.google.protobuf.DescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.field){if(!Array.isArray(e.field))throw TypeError(".google.protobuf.DescriptorProto.field: array expected");t.field=[];for(var o=0;o<e.field.length;++o){if("object"!=typeof e.field[o])throw TypeError(".google.protobuf.DescriptorProto.field: object expected");t.field[o]=s.google.protobuf.FieldDescriptorProto.fromObject(e.field[o])}}if(e.extension){if(!Array.isArray(e.extension))throw TypeError(".google.protobuf.DescriptorProto.extension: array expected");t.extension=[];for(o=0;o<e.extension.length;++o){if("object"!=typeof e.extension[o])throw TypeError(".google.protobuf.DescriptorProto.extension: object expected");t.extension[o]=s.google.protobuf.FieldDescriptorProto.fromObject(e.extension[o])}}if(e.nestedType){if(!Array.isArray(e.nestedType))throw TypeError(".google.protobuf.DescriptorProto.nestedType: array expected");t.nestedType=[];for(o=0;o<e.nestedType.length;++o){if("object"!=typeof e.nestedType[o])throw TypeError(".google.protobuf.DescriptorProto.nestedType: object expected");t.nestedType[o]=s.google.protobuf.DescriptorProto.fromObject(e.nestedType[o])}}if(e.enumType){if(!Array.isArray(e.enumType))throw TypeError(".google.protobuf.DescriptorProto.enumType: array expected");t.enumType=[];for(o=0;o<e.enumType.length;++o){if("object"!=typeof e.enumType[o])throw TypeError(".google.protobuf.DescriptorProto.enumType: object expected");t.enumType[o]=s.google.protobuf.EnumDescriptorProto.fromObject(e.enumType[o])}}if(e.extensionRange){if(!Array.isArray(e.extensionRange))throw TypeError(".google.protobuf.DescriptorProto.extensionRange: array expected");t.extensionRange=[];for(o=0;o<e.extensionRange.length;++o){if("object"!=typeof e.extensionRange[o])throw TypeError(".google.protobuf.DescriptorProto.extensionRange: object expected");t.extensionRange[o]=s.google.protobuf.DescriptorProto.ExtensionRange.fromObject(e.extensionRange[o])}}if(e.oneofDecl){if(!Array.isArray(e.oneofDecl))throw TypeError(".google.protobuf.DescriptorProto.oneofDecl: array expected");t.oneofDecl=[];for(o=0;o<e.oneofDecl.length;++o){if("object"!=typeof e.oneofDecl[o])throw TypeError(".google.protobuf.DescriptorProto.oneofDecl: object expected");t.oneofDecl[o]=s.google.protobuf.OneofDescriptorProto.fromObject(e.oneofDecl[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.DescriptorProto.options: object expected");t.options=s.google.protobuf.MessageOptions.fromObject(e.options)}if(e.reservedRange){if(!Array.isArray(e.reservedRange))throw TypeError(".google.protobuf.DescriptorProto.reservedRange: array expected");t.reservedRange=[];for(o=0;o<e.reservedRange.length;++o){if("object"!=typeof e.reservedRange[o])throw TypeError(".google.protobuf.DescriptorProto.reservedRange: object expected");t.reservedRange[o]=s.google.protobuf.DescriptorProto.ReservedRange.fromObject(e.reservedRange[o])}}if(e.reservedName){if(!Array.isArray(e.reservedName))throw TypeError(".google.protobuf.DescriptorProto.reservedName: array expected");t.reservedName=[];for(o=0;o<e.reservedName.length;++o)t.reservedName[o]=String(e.reservedName[o])}return t},P.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.field=[],o.nestedType=[],o.enumType=[],o.extensionRange=[],o.extension=[],o.oneofDecl=[],o.reservedRange=[],o.reservedName=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.field&&e.field.length){o.field=[];for(var r=0;r<e.field.length;++r)o.field[r]=s.google.protobuf.FieldDescriptorProto.toObject(e.field[r],t)}if(e.nestedType&&e.nestedType.length){o.nestedType=[];for(r=0;r<e.nestedType.length;++r)o.nestedType[r]=s.google.protobuf.DescriptorProto.toObject(e.nestedType[r],t)}if(e.enumType&&e.enumType.length){o.enumType=[];for(r=0;r<e.enumType.length;++r)o.enumType[r]=s.google.protobuf.EnumDescriptorProto.toObject(e.enumType[r],t)}if(e.extensionRange&&e.extensionRange.length){o.extensionRange=[];for(r=0;r<e.extensionRange.length;++r)o.extensionRange[r]=s.google.protobuf.DescriptorProto.ExtensionRange.toObject(e.extensionRange[r],t)}if(e.extension&&e.extension.length){o.extension=[];for(r=0;r<e.extension.length;++r)o.extension[r]=s.google.protobuf.FieldDescriptorProto.toObject(e.extension[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.MessageOptions.toObject(e.options,t)),e.oneofDecl&&e.oneofDecl.length){o.oneofDecl=[];for(r=0;r<e.oneofDecl.length;++r)o.oneofDecl[r]=s.google.protobuf.OneofDescriptorProto.toObject(e.oneofDecl[r],t)}if(e.reservedRange&&e.reservedRange.length){o.reservedRange=[];for(r=0;r<e.reservedRange.length;++r)o.reservedRange[r]=s.google.protobuf.DescriptorProto.ReservedRange.toObject(e.reservedRange[r],t)}if(e.reservedName&&e.reservedName.length){o.reservedName=[];for(r=0;r<e.reservedName.length;++r)o.reservedName[r]=e.reservedName[r]}return o},P.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},P.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto"},P.ExtensionRange=(Ze.prototype.start=0,Ze.prototype.end=0,Ze.prototype.options=null,Ze.fromObject=function(e){if(e instanceof s.google.protobuf.DescriptorProto.ExtensionRange)return e;var t=new s.google.protobuf.DescriptorProto.ExtensionRange;if(null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.DescriptorProto.ExtensionRange.options: object expected");t.options=s.google.protobuf.ExtensionRangeOptions.fromObject(e.options)}return t},Ze.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0,o.options=null),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.ExtensionRangeOptions.toObject(e.options,t)),o},Ze.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ze.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto.ExtensionRange"},Ze),P.ReservedRange=(Qe.prototype.start=0,Qe.prototype.end=0,Qe.fromObject=function(e){var t;return e instanceof s.google.protobuf.DescriptorProto.ReservedRange?e:(t=new s.google.protobuf.DescriptorProto.ReservedRange,null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),t)},Qe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),o},Qe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Qe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto.ReservedRange"},Qe),P),o.ExtensionRangeOptions=($e.prototype.uninterpretedOption=a.emptyArray,$e.prototype.declaration=a.emptyArray,$e.prototype.features=null,$e.prototype.verification=1,$e.fromObject=function(e){if(e instanceof s.google.protobuf.ExtensionRangeOptions)return e;var t=new s.google.protobuf.ExtensionRangeOptions;if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.ExtensionRangeOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.ExtensionRangeOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e.declaration){if(!Array.isArray(e.declaration))throw TypeError(".google.protobuf.ExtensionRangeOptions.declaration: array expected");t.declaration=[];for(o=0;o<e.declaration.length;++o){if("object"!=typeof e.declaration[o])throw TypeError(".google.protobuf.ExtensionRangeOptions.declaration: object expected");t.declaration[o]=s.google.protobuf.ExtensionRangeOptions.Declaration.fromObject(e.declaration[o])}}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.ExtensionRangeOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}switch(e.verification){case"DECLARATION":case 0:t.verification=0;break;default:"number"==typeof e.verification&&(t.verification=e.verification);break;case"UNVERIFIED":case 1:t.verification=1}return t},$e.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.declaration=[],o.uninterpretedOption=[]),t.defaults&&(o.verification=t.enums===String?"UNVERIFIED":1,o.features=null),e.declaration&&e.declaration.length){o.declaration=[];for(var r=0;r<e.declaration.length;++r)o.declaration[r]=s.google.protobuf.ExtensionRangeOptions.Declaration.toObject(e.declaration[r],t)}if(null!=e.verification&&e.hasOwnProperty("verification")&&(o.verification=t.enums!==String||void 0===s.google.protobuf.ExtensionRangeOptions.VerificationState[e.verification]?e.verification:s.google.protobuf.ExtensionRangeOptions.VerificationState[e.verification]),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},$e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$e.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ExtensionRangeOptions"},$e.Declaration=(et.prototype.number=0,et.prototype.fullName="",et.prototype.type="",et.prototype.reserved=!1,et.prototype.repeated=!1,et.fromObject=function(e){var t;return e instanceof s.google.protobuf.ExtensionRangeOptions.Declaration?e:(t=new s.google.protobuf.ExtensionRangeOptions.Declaration,null!=e.number&&(t.number=0|e.number),null!=e.fullName&&(t.fullName=String(e.fullName)),null!=e.type&&(t.type=String(e.type)),null!=e.reserved&&(t.reserved=Boolean(e.reserved)),null!=e.repeated&&(t.repeated=Boolean(e.repeated)),t)},et.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.number=0,o.fullName="",o.type="",o.reserved=!1,o.repeated=!1),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.fullName&&e.hasOwnProperty("fullName")&&(o.fullName=e.fullName),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),null!=e.reserved&&e.hasOwnProperty("reserved")&&(o.reserved=e.reserved),null!=e.repeated&&e.hasOwnProperty("repeated")&&(o.repeated=e.repeated),o},et.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},et.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ExtensionRangeOptions.Declaration"},et),$e.VerificationState=(n={},(e=Object.create(n))[n[0]="DECLARATION"]="DECLARATION",e[n[1]="UNVERIFIED"]="UNVERIFIED",e),$e),o.FieldDescriptorProto=(D.prototype.name="",D.prototype.number=0,D.prototype.label=1,D.prototype.type=1,D.prototype.typeName="",D.prototype.extendee="",D.prototype.defaultValue="",D.prototype.oneofIndex=0,D.prototype.jsonName="",D.prototype.options=null,D.prototype.proto3Optional=!1,D.fromObject=function(e){if(e instanceof s.google.protobuf.FieldDescriptorProto)return e;var t=new s.google.protobuf.FieldDescriptorProto;switch(null!=e.name&&(t.name=String(e.name)),null!=e.number&&(t.number=0|e.number),e.label){default:"number"==typeof e.label&&(t.label=e.label);break;case"LABEL_OPTIONAL":case 1:t.label=1;break;case"LABEL_REPEATED":case 3:t.label=3;break;case"LABEL_REQUIRED":case 2:t.label=2}switch(e.type){default:"number"==typeof e.type&&(t.type=e.type);break;case"TYPE_DOUBLE":case 1:t.type=1;break;case"TYPE_FLOAT":case 2:t.type=2;break;case"TYPE_INT64":case 3:t.type=3;break;case"TYPE_UINT64":case 4:t.type=4;break;case"TYPE_INT32":case 5:t.type=5;break;case"TYPE_FIXED64":case 6:t.type=6;break;case"TYPE_FIXED32":case 7:t.type=7;break;case"TYPE_BOOL":case 8:t.type=8;break;case"TYPE_STRING":case 9:t.type=9;break;case"TYPE_GROUP":case 10:t.type=10;break;case"TYPE_MESSAGE":case 11:t.type=11;break;case"TYPE_BYTES":case 12:t.type=12;break;case"TYPE_UINT32":case 13:t.type=13;break;case"TYPE_ENUM":case 14:t.type=14;break;case"TYPE_SFIXED32":case 15:t.type=15;break;case"TYPE_SFIXED64":case 16:t.type=16;break;case"TYPE_SINT32":case 17:t.type=17;break;case"TYPE_SINT64":case 18:t.type=18}if(null!=e.typeName&&(t.typeName=String(e.typeName)),null!=e.extendee&&(t.extendee=String(e.extendee)),null!=e.defaultValue&&(t.defaultValue=String(e.defaultValue)),null!=e.oneofIndex&&(t.oneofIndex=0|e.oneofIndex),null!=e.jsonName&&(t.jsonName=String(e.jsonName)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.FieldDescriptorProto.options: object expected");t.options=s.google.protobuf.FieldOptions.fromObject(e.options)}return null!=e.proto3Optional&&(t.proto3Optional=Boolean(e.proto3Optional)),t},D.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.extendee="",o.number=0,o.label=t.enums===String?"LABEL_OPTIONAL":1,o.type=t.enums===String?"TYPE_DOUBLE":1,o.typeName="",o.defaultValue="",o.options=null,o.oneofIndex=0,o.jsonName="",o.proto3Optional=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.extendee&&e.hasOwnProperty("extendee")&&(o.extendee=e.extendee),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.label&&e.hasOwnProperty("label")&&(o.label=t.enums!==String||void 0===s.google.protobuf.FieldDescriptorProto.Label[e.label]?e.label:s.google.protobuf.FieldDescriptorProto.Label[e.label]),null!=e.type&&e.hasOwnProperty("type")&&(o.type=t.enums!==String||void 0===s.google.protobuf.FieldDescriptorProto.Type[e.type]?e.type:s.google.protobuf.FieldDescriptorProto.Type[e.type]),null!=e.typeName&&e.hasOwnProperty("typeName")&&(o.typeName=e.typeName),null!=e.defaultValue&&e.hasOwnProperty("defaultValue")&&(o.defaultValue=e.defaultValue),null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.FieldOptions.toObject(e.options,t)),null!=e.oneofIndex&&e.hasOwnProperty("oneofIndex")&&(o.oneofIndex=e.oneofIndex),null!=e.jsonName&&e.hasOwnProperty("jsonName")&&(o.jsonName=e.jsonName),null!=e.proto3Optional&&e.hasOwnProperty("proto3Optional")&&(o.proto3Optional=e.proto3Optional),o},D.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},D.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldDescriptorProto"},D.Type=(n={},(e=Object.create(n))[n[1]="TYPE_DOUBLE"]="TYPE_DOUBLE",e[n[2]="TYPE_FLOAT"]="TYPE_FLOAT",e[n[3]="TYPE_INT64"]="TYPE_INT64",e[n[4]="TYPE_UINT64"]="TYPE_UINT64",e[n[5]="TYPE_INT32"]="TYPE_INT32",e[n[6]="TYPE_FIXED64"]="TYPE_FIXED64",e[n[7]="TYPE_FIXED32"]="TYPE_FIXED32",e[n[8]="TYPE_BOOL"]="TYPE_BOOL",e[n[9]="TYPE_STRING"]="TYPE_STRING",e[n[10]="TYPE_GROUP"]="TYPE_GROUP",e[n[11]="TYPE_MESSAGE"]="TYPE_MESSAGE",e[n[12]="TYPE_BYTES"]="TYPE_BYTES",e[n[13]="TYPE_UINT32"]="TYPE_UINT32",e[n[14]="TYPE_ENUM"]="TYPE_ENUM",e[n[15]="TYPE_SFIXED32"]="TYPE_SFIXED32",e[n[16]="TYPE_SFIXED64"]="TYPE_SFIXED64",e[n[17]="TYPE_SINT32"]="TYPE_SINT32",e[n[18]="TYPE_SINT64"]="TYPE_SINT64",e),D.Label=(n={},(e=Object.create(n))[n[1]="LABEL_OPTIONAL"]="LABEL_OPTIONAL",e[n[3]="LABEL_REPEATED"]="LABEL_REPEATED",e[n[2]="LABEL_REQUIRED"]="LABEL_REQUIRED",e),D),o.OneofDescriptorProto=(tt.prototype.name="",tt.prototype.options=null,tt.fromObject=function(e){if(e instanceof s.google.protobuf.OneofDescriptorProto)return e;var t=new s.google.protobuf.OneofDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.OneofDescriptorProto.options: object expected");t.options=s.google.protobuf.OneofOptions.fromObject(e.options)}return t},tt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.OneofOptions.toObject(e.options,t)),o},tt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},tt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.OneofDescriptorProto"},tt),o.EnumDescriptorProto=(ot.prototype.name="",ot.prototype.value=a.emptyArray,ot.prototype.options=null,ot.prototype.reservedRange=a.emptyArray,ot.prototype.reservedName=a.emptyArray,ot.fromObject=function(e){if(e instanceof s.google.protobuf.EnumDescriptorProto)return e;var t=new s.google.protobuf.EnumDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.value){if(!Array.isArray(e.value))throw TypeError(".google.protobuf.EnumDescriptorProto.value: array expected");t.value=[];for(var o=0;o<e.value.length;++o){if("object"!=typeof e.value[o])throw TypeError(".google.protobuf.EnumDescriptorProto.value: object expected");t.value[o]=s.google.protobuf.EnumValueDescriptorProto.fromObject(e.value[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.EnumDescriptorProto.options: object expected");t.options=s.google.protobuf.EnumOptions.fromObject(e.options)}if(e.reservedRange){if(!Array.isArray(e.reservedRange))throw TypeError(".google.protobuf.EnumDescriptorProto.reservedRange: array expected");t.reservedRange=[];for(o=0;o<e.reservedRange.length;++o){if("object"!=typeof e.reservedRange[o])throw TypeError(".google.protobuf.EnumDescriptorProto.reservedRange: object expected");t.reservedRange[o]=s.google.protobuf.EnumDescriptorProto.EnumReservedRange.fromObject(e.reservedRange[o])}}if(e.reservedName){if(!Array.isArray(e.reservedName))throw TypeError(".google.protobuf.EnumDescriptorProto.reservedName: array expected");t.reservedName=[];for(o=0;o<e.reservedName.length;++o)t.reservedName[o]=String(e.reservedName[o])}return t},ot.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.value=[],o.reservedRange=[],o.reservedName=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.value&&e.value.length){o.value=[];for(var r=0;r<e.value.length;++r)o.value[r]=s.google.protobuf.EnumValueDescriptorProto.toObject(e.value[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.EnumOptions.toObject(e.options,t)),e.reservedRange&&e.reservedRange.length){o.reservedRange=[];for(r=0;r<e.reservedRange.length;++r)o.reservedRange[r]=s.google.protobuf.EnumDescriptorProto.EnumReservedRange.toObject(e.reservedRange[r],t)}if(e.reservedName&&e.reservedName.length){o.reservedName=[];for(r=0;r<e.reservedName.length;++r)o.reservedName[r]=e.reservedName[r]}return o},ot.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ot.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumDescriptorProto"},ot.EnumReservedRange=(rt.prototype.start=0,rt.prototype.end=0,rt.fromObject=function(e){var t;return e instanceof s.google.protobuf.EnumDescriptorProto.EnumReservedRange?e:(t=new s.google.protobuf.EnumDescriptorProto.EnumReservedRange,null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),t)},rt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),o},rt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},rt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumDescriptorProto.EnumReservedRange"},rt),ot),o.EnumValueDescriptorProto=(nt.prototype.name="",nt.prototype.number=0,nt.prototype.options=null,nt.fromObject=function(e){if(e instanceof s.google.protobuf.EnumValueDescriptorProto)return e;var t=new s.google.protobuf.EnumValueDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.number&&(t.number=0|e.number),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.EnumValueDescriptorProto.options: object expected");t.options=s.google.protobuf.EnumValueOptions.fromObject(e.options)}return t},nt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.number=0,o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.EnumValueOptions.toObject(e.options,t)),o},nt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},nt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumValueDescriptorProto"},nt),o.ServiceDescriptorProto=(it.prototype.name="",it.prototype.method=a.emptyArray,it.prototype.options=null,it.fromObject=function(e){if(e instanceof s.google.protobuf.ServiceDescriptorProto)return e;var t=new s.google.protobuf.ServiceDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.method){if(!Array.isArray(e.method))throw TypeError(".google.protobuf.ServiceDescriptorProto.method: array expected");t.method=[];for(var o=0;o<e.method.length;++o){if("object"!=typeof e.method[o])throw TypeError(".google.protobuf.ServiceDescriptorProto.method: object expected");t.method[o]=s.google.protobuf.MethodDescriptorProto.fromObject(e.method[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.ServiceDescriptorProto.options: object expected");t.options=s.google.protobuf.ServiceOptions.fromObject(e.options)}return t},it.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.method=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.method&&e.method.length){o.method=[];for(var r=0;r<e.method.length;++r)o.method[r]=s.google.protobuf.MethodDescriptorProto.toObject(e.method[r],t)}return null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.ServiceOptions.toObject(e.options,t)),o},it.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},it.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ServiceDescriptorProto"},it),o.MethodDescriptorProto=(at.prototype.name="",at.prototype.inputType="",at.prototype.outputType="",at.prototype.options=null,at.prototype.clientStreaming=!1,at.prototype.serverStreaming=!1,at.fromObject=function(e){if(e instanceof s.google.protobuf.MethodDescriptorProto)return e;var t=new s.google.protobuf.MethodDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.inputType&&(t.inputType=String(e.inputType)),null!=e.outputType&&(t.outputType=String(e.outputType)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.MethodDescriptorProto.options: object expected");t.options=s.google.protobuf.MethodOptions.fromObject(e.options)}return null!=e.clientStreaming&&(t.clientStreaming=Boolean(e.clientStreaming)),null!=e.serverStreaming&&(t.serverStreaming=Boolean(e.serverStreaming)),t},at.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.inputType="",o.outputType="",o.options=null,o.clientStreaming=!1,o.serverStreaming=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.inputType&&e.hasOwnProperty("inputType")&&(o.inputType=e.inputType),null!=e.outputType&&e.hasOwnProperty("outputType")&&(o.outputType=e.outputType),null!=e.options&&e.hasOwnProperty("options")&&(o.options=s.google.protobuf.MethodOptions.toObject(e.options,t)),null!=e.clientStreaming&&e.hasOwnProperty("clientStreaming")&&(o.clientStreaming=e.clientStreaming),null!=e.serverStreaming&&e.hasOwnProperty("serverStreaming")&&(o.serverStreaming=e.serverStreaming),o},at.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},at.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MethodDescriptorProto"},at),o.FileOptions=(w.prototype.javaPackage="",w.prototype.javaOuterClassname="",w.prototype.javaMultipleFiles=!1,w.prototype.javaGenerateEqualsAndHash=!1,w.prototype.javaStringCheckUtf8=!1,w.prototype.optimizeFor=1,w.prototype.goPackage="",w.prototype.ccGenericServices=!1,w.prototype.javaGenericServices=!1,w.prototype.pyGenericServices=!1,w.prototype.deprecated=!1,w.prototype.ccEnableArenas=!0,w.prototype.objcClassPrefix="",w.prototype.csharpNamespace="",w.prototype.swiftPrefix="",w.prototype.phpClassPrefix="",w.prototype.phpNamespace="",w.prototype.phpMetadataNamespace="",w.prototype.rubyPackage="",w.prototype.features=null,w.prototype.uninterpretedOption=a.emptyArray,w.prototype[".google.api.resourceDefinition"]=a.emptyArray,w.fromObject=function(e){if(e instanceof s.google.protobuf.FileOptions)return e;var t=new s.google.protobuf.FileOptions;switch(null!=e.javaPackage&&(t.javaPackage=String(e.javaPackage)),null!=e.javaOuterClassname&&(t.javaOuterClassname=String(e.javaOuterClassname)),null!=e.javaMultipleFiles&&(t.javaMultipleFiles=Boolean(e.javaMultipleFiles)),null!=e.javaGenerateEqualsAndHash&&(t.javaGenerateEqualsAndHash=Boolean(e.javaGenerateEqualsAndHash)),null!=e.javaStringCheckUtf8&&(t.javaStringCheckUtf8=Boolean(e.javaStringCheckUtf8)),e.optimizeFor){default:"number"==typeof e.optimizeFor&&(t.optimizeFor=e.optimizeFor);break;case"SPEED":case 1:t.optimizeFor=1;break;case"CODE_SIZE":case 2:t.optimizeFor=2;break;case"LITE_RUNTIME":case 3:t.optimizeFor=3}if(null!=e.goPackage&&(t.goPackage=String(e.goPackage)),null!=e.ccGenericServices&&(t.ccGenericServices=Boolean(e.ccGenericServices)),null!=e.javaGenericServices&&(t.javaGenericServices=Boolean(e.javaGenericServices)),null!=e.pyGenericServices&&(t.pyGenericServices=Boolean(e.pyGenericServices)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.ccEnableArenas&&(t.ccEnableArenas=Boolean(e.ccEnableArenas)),null!=e.objcClassPrefix&&(t.objcClassPrefix=String(e.objcClassPrefix)),null!=e.csharpNamespace&&(t.csharpNamespace=String(e.csharpNamespace)),null!=e.swiftPrefix&&(t.swiftPrefix=String(e.swiftPrefix)),null!=e.phpClassPrefix&&(t.phpClassPrefix=String(e.phpClassPrefix)),null!=e.phpNamespace&&(t.phpNamespace=String(e.phpNamespace)),null!=e.phpMetadataNamespace&&(t.phpMetadataNamespace=String(e.phpMetadataNamespace)),null!=e.rubyPackage&&(t.rubyPackage=String(e.rubyPackage)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FileOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.FileOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.FileOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e[".google.api.resourceDefinition"]){if(!Array.isArray(e[".google.api.resourceDefinition"]))throw TypeError(".google.protobuf.FileOptions..google.api.resourceDefinition: array expected");t[".google.api.resourceDefinition"]=[];for(o=0;o<e[".google.api.resourceDefinition"].length;++o){if("object"!=typeof e[".google.api.resourceDefinition"][o])throw TypeError(".google.protobuf.FileOptions..google.api.resourceDefinition: object expected");t[".google.api.resourceDefinition"][o]=s.google.api.ResourceDescriptor.fromObject(e[".google.api.resourceDefinition"][o])}}return t},w.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[],o[".google.api.resourceDefinition"]=[]),t.defaults&&(o.javaPackage="",o.javaOuterClassname="",o.optimizeFor=t.enums===String?"SPEED":1,o.javaMultipleFiles=!1,o.goPackage="",o.ccGenericServices=!1,o.javaGenericServices=!1,o.pyGenericServices=!1,o.javaGenerateEqualsAndHash=!1,o.deprecated=!1,o.javaStringCheckUtf8=!1,o.ccEnableArenas=!0,o.objcClassPrefix="",o.csharpNamespace="",o.swiftPrefix="",o.phpClassPrefix="",o.phpNamespace="",o.phpMetadataNamespace="",o.rubyPackage="",o.features=null),null!=e.javaPackage&&e.hasOwnProperty("javaPackage")&&(o.javaPackage=e.javaPackage),null!=e.javaOuterClassname&&e.hasOwnProperty("javaOuterClassname")&&(o.javaOuterClassname=e.javaOuterClassname),null!=e.optimizeFor&&e.hasOwnProperty("optimizeFor")&&(o.optimizeFor=t.enums!==String||void 0===s.google.protobuf.FileOptions.OptimizeMode[e.optimizeFor]?e.optimizeFor:s.google.protobuf.FileOptions.OptimizeMode[e.optimizeFor]),null!=e.javaMultipleFiles&&e.hasOwnProperty("javaMultipleFiles")&&(o.javaMultipleFiles=e.javaMultipleFiles),null!=e.goPackage&&e.hasOwnProperty("goPackage")&&(o.goPackage=e.goPackage),null!=e.ccGenericServices&&e.hasOwnProperty("ccGenericServices")&&(o.ccGenericServices=e.ccGenericServices),null!=e.javaGenericServices&&e.hasOwnProperty("javaGenericServices")&&(o.javaGenericServices=e.javaGenericServices),null!=e.pyGenericServices&&e.hasOwnProperty("pyGenericServices")&&(o.pyGenericServices=e.pyGenericServices),null!=e.javaGenerateEqualsAndHash&&e.hasOwnProperty("javaGenerateEqualsAndHash")&&(o.javaGenerateEqualsAndHash=e.javaGenerateEqualsAndHash),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.javaStringCheckUtf8&&e.hasOwnProperty("javaStringCheckUtf8")&&(o.javaStringCheckUtf8=e.javaStringCheckUtf8),null!=e.ccEnableArenas&&e.hasOwnProperty("ccEnableArenas")&&(o.ccEnableArenas=e.ccEnableArenas),null!=e.objcClassPrefix&&e.hasOwnProperty("objcClassPrefix")&&(o.objcClassPrefix=e.objcClassPrefix),null!=e.csharpNamespace&&e.hasOwnProperty("csharpNamespace")&&(o.csharpNamespace=e.csharpNamespace),null!=e.swiftPrefix&&e.hasOwnProperty("swiftPrefix")&&(o.swiftPrefix=e.swiftPrefix),null!=e.phpClassPrefix&&e.hasOwnProperty("phpClassPrefix")&&(o.phpClassPrefix=e.phpClassPrefix),null!=e.phpNamespace&&e.hasOwnProperty("phpNamespace")&&(o.phpNamespace=e.phpNamespace),null!=e.phpMetadataNamespace&&e.hasOwnProperty("phpMetadataNamespace")&&(o.phpMetadataNamespace=e.phpMetadataNamespace),null!=e.rubyPackage&&e.hasOwnProperty("rubyPackage")&&(o.rubyPackage=e.rubyPackage),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(e[".google.api.resourceDefinition"]&&e[".google.api.resourceDefinition"].length){o[".google.api.resourceDefinition"]=[];for(r=0;r<e[".google.api.resourceDefinition"].length;++r)o[".google.api.resourceDefinition"][r]=s.google.api.ResourceDescriptor.toObject(e[".google.api.resourceDefinition"][r],t)}return o},w.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},w.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileOptions"},w.OptimizeMode=(n={},(e=Object.create(n))[n[1]="SPEED"]="SPEED",e[n[2]="CODE_SIZE"]="CODE_SIZE",e[n[3]="LITE_RUNTIME"]="LITE_RUNTIME",e),w),o.MessageOptions=(k.prototype.messageSetWireFormat=!1,k.prototype.noStandardDescriptorAccessor=!1,k.prototype.deprecated=!1,k.prototype.mapEntry=!1,k.prototype.deprecatedLegacyJsonFieldConflicts=!1,k.prototype.features=null,k.prototype.uninterpretedOption=a.emptyArray,k.prototype[".google.api.resource"]=null,k.fromObject=function(e){if(e instanceof s.google.protobuf.MessageOptions)return e;var t=new s.google.protobuf.MessageOptions;if(null!=e.messageSetWireFormat&&(t.messageSetWireFormat=Boolean(e.messageSetWireFormat)),null!=e.noStandardDescriptorAccessor&&(t.noStandardDescriptorAccessor=Boolean(e.noStandardDescriptorAccessor)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.mapEntry&&(t.mapEntry=Boolean(e.mapEntry)),null!=e.deprecatedLegacyJsonFieldConflicts&&(t.deprecatedLegacyJsonFieldConflicts=Boolean(e.deprecatedLegacyJsonFieldConflicts)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.MessageOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.MessageOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.MessageOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(null!=e[".google.api.resource"]){if("object"!=typeof e[".google.api.resource"])throw TypeError(".google.protobuf.MessageOptions..google.api.resource: object expected");t[".google.api.resource"]=s.google.api.ResourceDescriptor.fromObject(e[".google.api.resource"])}return t},k.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.messageSetWireFormat=!1,o.noStandardDescriptorAccessor=!1,o.deprecated=!1,o.mapEntry=!1,o.deprecatedLegacyJsonFieldConflicts=!1,o.features=null,o[".google.api.resource"]=null),null!=e.messageSetWireFormat&&e.hasOwnProperty("messageSetWireFormat")&&(o.messageSetWireFormat=e.messageSetWireFormat),null!=e.noStandardDescriptorAccessor&&e.hasOwnProperty("noStandardDescriptorAccessor")&&(o.noStandardDescriptorAccessor=e.noStandardDescriptorAccessor),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.mapEntry&&e.hasOwnProperty("mapEntry")&&(o.mapEntry=e.mapEntry),null!=e.deprecatedLegacyJsonFieldConflicts&&e.hasOwnProperty("deprecatedLegacyJsonFieldConflicts")&&(o.deprecatedLegacyJsonFieldConflicts=e.deprecatedLegacyJsonFieldConflicts),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return null!=e[".google.api.resource"]&&e.hasOwnProperty(".google.api.resource")&&(o[".google.api.resource"]=s.google.api.ResourceDescriptor.toObject(e[".google.api.resource"],t)),o},k.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},k.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MessageOptions"},k),o.FieldOptions=(R.prototype.ctype=0,R.prototype.packed=!1,R.prototype.jstype=0,R.prototype.lazy=!1,R.prototype.unverifiedLazy=!1,R.prototype.deprecated=!1,R.prototype.weak=!1,R.prototype.debugRedact=!1,R.prototype.retention=0,R.prototype.targets=a.emptyArray,R.prototype.editionDefaults=a.emptyArray,R.prototype.features=null,R.prototype.uninterpretedOption=a.emptyArray,R.prototype[".google.api.fieldBehavior"]=a.emptyArray,R.prototype[".google.api.resourceReference"]=null,R.fromObject=function(e){if(e instanceof s.google.protobuf.FieldOptions)return e;var t=new s.google.protobuf.FieldOptions;switch(e.ctype){default:"number"==typeof e.ctype&&(t.ctype=e.ctype);break;case"STRING":case 0:t.ctype=0;break;case"CORD":case 1:t.ctype=1;break;case"STRING_PIECE":case 2:t.ctype=2}switch(null!=e.packed&&(t.packed=Boolean(e.packed)),e.jstype){default:"number"==typeof e.jstype&&(t.jstype=e.jstype);break;case"JS_NORMAL":case 0:t.jstype=0;break;case"JS_STRING":case 1:t.jstype=1;break;case"JS_NUMBER":case 2:t.jstype=2}switch(null!=e.lazy&&(t.lazy=Boolean(e.lazy)),null!=e.unverifiedLazy&&(t.unverifiedLazy=Boolean(e.unverifiedLazy)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.weak&&(t.weak=Boolean(e.weak)),null!=e.debugRedact&&(t.debugRedact=Boolean(e.debugRedact)),e.retention){default:"number"==typeof e.retention&&(t.retention=e.retention);break;case"RETENTION_UNKNOWN":case 0:t.retention=0;break;case"RETENTION_RUNTIME":case 1:t.retention=1;break;case"RETENTION_SOURCE":case 2:t.retention=2}if(e.targets){if(!Array.isArray(e.targets))throw TypeError(".google.protobuf.FieldOptions.targets: array expected");t.targets=[];for(var o=0;o<e.targets.length;++o)switch(e.targets[o]){default:if("number"==typeof e.targets[o]){t.targets[o]=e.targets[o];break}case"TARGET_TYPE_UNKNOWN":case 0:t.targets[o]=0;break;case"TARGET_TYPE_FILE":case 1:t.targets[o]=1;break;case"TARGET_TYPE_EXTENSION_RANGE":case 2:t.targets[o]=2;break;case"TARGET_TYPE_MESSAGE":case 3:t.targets[o]=3;break;case"TARGET_TYPE_FIELD":case 4:t.targets[o]=4;break;case"TARGET_TYPE_ONEOF":case 5:t.targets[o]=5;break;case"TARGET_TYPE_ENUM":case 6:t.targets[o]=6;break;case"TARGET_TYPE_ENUM_ENTRY":case 7:t.targets[o]=7;break;case"TARGET_TYPE_SERVICE":case 8:t.targets[o]=8;break;case"TARGET_TYPE_METHOD":case 9:t.targets[o]=9}}if(e.editionDefaults){if(!Array.isArray(e.editionDefaults))throw TypeError(".google.protobuf.FieldOptions.editionDefaults: array expected");t.editionDefaults=[];for(o=0;o<e.editionDefaults.length;++o){if("object"!=typeof e.editionDefaults[o])throw TypeError(".google.protobuf.FieldOptions.editionDefaults: object expected");t.editionDefaults[o]=s.google.protobuf.FieldOptions.EditionDefault.fromObject(e.editionDefaults[o])}}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FieldOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.FieldOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.FieldOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e[".google.api.fieldBehavior"]){if(!Array.isArray(e[".google.api.fieldBehavior"]))throw TypeError(".google.protobuf.FieldOptions..google.api.fieldBehavior: array expected");t[".google.api.fieldBehavior"]=[];for(o=0;o<e[".google.api.fieldBehavior"].length;++o)switch(e[".google.api.fieldBehavior"][o]){default:if("number"==typeof e[".google.api.fieldBehavior"][o]){t[".google.api.fieldBehavior"][o]=e[".google.api.fieldBehavior"][o];break}case"FIELD_BEHAVIOR_UNSPECIFIED":case 0:t[".google.api.fieldBehavior"][o]=0;break;case"OPTIONAL":case 1:t[".google.api.fieldBehavior"][o]=1;break;case"REQUIRED":case 2:t[".google.api.fieldBehavior"][o]=2;break;case"OUTPUT_ONLY":case 3:t[".google.api.fieldBehavior"][o]=3;break;case"INPUT_ONLY":case 4:t[".google.api.fieldBehavior"][o]=4;break;case"IMMUTABLE":case 5:t[".google.api.fieldBehavior"][o]=5;break;case"UNORDERED_LIST":case 6:t[".google.api.fieldBehavior"][o]=6;break;case"NON_EMPTY_DEFAULT":case 7:t[".google.api.fieldBehavior"][o]=7;break;case"IDENTIFIER":case 8:t[".google.api.fieldBehavior"][o]=8}}if(null!=e[".google.api.resourceReference"]){if("object"!=typeof e[".google.api.resourceReference"])throw TypeError(".google.protobuf.FieldOptions..google.api.resourceReference: object expected");t[".google.api.resourceReference"]=s.google.api.ResourceReference.fromObject(e[".google.api.resourceReference"])}return t},R.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targets=[],o.editionDefaults=[],o.uninterpretedOption=[],o[".google.api.fieldBehavior"]=[]),t.defaults&&(o.ctype=t.enums===String?"STRING":0,o.packed=!1,o.deprecated=!1,o.lazy=!1,o.jstype=t.enums===String?"JS_NORMAL":0,o.weak=!1,o.unverifiedLazy=!1,o.debugRedact=!1,o.retention=t.enums===String?"RETENTION_UNKNOWN":0,o.features=null,o[".google.api.resourceReference"]=null),null!=e.ctype&&e.hasOwnProperty("ctype")&&(o.ctype=t.enums!==String||void 0===s.google.protobuf.FieldOptions.CType[e.ctype]?e.ctype:s.google.protobuf.FieldOptions.CType[e.ctype]),null!=e.packed&&e.hasOwnProperty("packed")&&(o.packed=e.packed),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.lazy&&e.hasOwnProperty("lazy")&&(o.lazy=e.lazy),null!=e.jstype&&e.hasOwnProperty("jstype")&&(o.jstype=t.enums!==String||void 0===s.google.protobuf.FieldOptions.JSType[e.jstype]?e.jstype:s.google.protobuf.FieldOptions.JSType[e.jstype]),null!=e.weak&&e.hasOwnProperty("weak")&&(o.weak=e.weak),null!=e.unverifiedLazy&&e.hasOwnProperty("unverifiedLazy")&&(o.unverifiedLazy=e.unverifiedLazy),null!=e.debugRedact&&e.hasOwnProperty("debugRedact")&&(o.debugRedact=e.debugRedact),null!=e.retention&&e.hasOwnProperty("retention")&&(o.retention=t.enums!==String||void 0===s.google.protobuf.FieldOptions.OptionRetention[e.retention]?e.retention:s.google.protobuf.FieldOptions.OptionRetention[e.retention]),e.targets&&e.targets.length){o.targets=[];for(var r=0;r<e.targets.length;++r)o.targets[r]=t.enums!==String||void 0===s.google.protobuf.FieldOptions.OptionTargetType[e.targets[r]]?e.targets[r]:s.google.protobuf.FieldOptions.OptionTargetType[e.targets[r]]}if(e.editionDefaults&&e.editionDefaults.length){o.editionDefaults=[];for(r=0;r<e.editionDefaults.length;++r)o.editionDefaults[r]=s.google.protobuf.FieldOptions.EditionDefault.toObject(e.editionDefaults[r],t)}if(null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(e[".google.api.fieldBehavior"]&&e[".google.api.fieldBehavior"].length){o[".google.api.fieldBehavior"]=[];for(r=0;r<e[".google.api.fieldBehavior"].length;++r)o[".google.api.fieldBehavior"][r]=t.enums!==String||void 0===s.google.api.FieldBehavior[e[".google.api.fieldBehavior"][r]]?e[".google.api.fieldBehavior"][r]:s.google.api.FieldBehavior[e[".google.api.fieldBehavior"][r]]}return null!=e[".google.api.resourceReference"]&&e.hasOwnProperty(".google.api.resourceReference")&&(o[".google.api.resourceReference"]=s.google.api.ResourceReference.toObject(e[".google.api.resourceReference"],t)),o},R.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},R.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldOptions"},R.CType=(n={},(e=Object.create(n))[n[0]="STRING"]="STRING",e[n[1]="CORD"]="CORD",e[n[2]="STRING_PIECE"]="STRING_PIECE",e),R.JSType=(n={},(e=Object.create(n))[n[0]="JS_NORMAL"]="JS_NORMAL",e[n[1]="JS_STRING"]="JS_STRING",e[n[2]="JS_NUMBER"]="JS_NUMBER",e),R.OptionRetention=(n={},(e=Object.create(n))[n[0]="RETENTION_UNKNOWN"]="RETENTION_UNKNOWN",e[n[1]="RETENTION_RUNTIME"]="RETENTION_RUNTIME",e[n[2]="RETENTION_SOURCE"]="RETENTION_SOURCE",e),R.OptionTargetType=(n={},(e=Object.create(n))[n[0]="TARGET_TYPE_UNKNOWN"]="TARGET_TYPE_UNKNOWN",e[n[1]="TARGET_TYPE_FILE"]="TARGET_TYPE_FILE",e[n[2]="TARGET_TYPE_EXTENSION_RANGE"]="TARGET_TYPE_EXTENSION_RANGE",e[n[3]="TARGET_TYPE_MESSAGE"]="TARGET_TYPE_MESSAGE",e[n[4]="TARGET_TYPE_FIELD"]="TARGET_TYPE_FIELD",e[n[5]="TARGET_TYPE_ONEOF"]="TARGET_TYPE_ONEOF",e[n[6]="TARGET_TYPE_ENUM"]="TARGET_TYPE_ENUM",e[n[7]="TARGET_TYPE_ENUM_ENTRY"]="TARGET_TYPE_ENUM_ENTRY",e[n[8]="TARGET_TYPE_SERVICE"]="TARGET_TYPE_SERVICE",e[n[9]="TARGET_TYPE_METHOD"]="TARGET_TYPE_METHOD",e),R.EditionDefault=(st.prototype.edition=0,st.prototype.value="",st.fromObject=function(e){if(e instanceof s.google.protobuf.FieldOptions.EditionDefault)return e;var t=new s.google.protobuf.FieldOptions.EditionDefault;switch(e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}return null!=e.value&&(t.value=String(e.value)),t},st.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value="",o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===s.google.protobuf.Edition[e.edition]?e.edition:s.google.protobuf.Edition[e.edition]),o},st.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},st.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldOptions.EditionDefault"},st),R),o.OneofOptions=(lt.prototype.features=null,lt.prototype.uninterpretedOption=a.emptyArray,lt.fromObject=function(e){if(e instanceof s.google.protobuf.OneofOptions)return e;var t=new s.google.protobuf.OneofOptions;if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.OneofOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.OneofOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.OneofOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},lt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.features=null),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},lt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},lt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.OneofOptions"},lt),o.EnumOptions=(pt.prototype.allowAlias=!1,pt.prototype.deprecated=!1,pt.prototype.deprecatedLegacyJsonFieldConflicts=!1,pt.prototype.features=null,pt.prototype.uninterpretedOption=a.emptyArray,pt.fromObject=function(e){if(e instanceof s.google.protobuf.EnumOptions)return e;var t=new s.google.protobuf.EnumOptions;if(null!=e.allowAlias&&(t.allowAlias=Boolean(e.allowAlias)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.deprecatedLegacyJsonFieldConflicts&&(t.deprecatedLegacyJsonFieldConflicts=Boolean(e.deprecatedLegacyJsonFieldConflicts)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.EnumOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.EnumOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.EnumOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},pt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.allowAlias=!1,o.deprecated=!1,o.deprecatedLegacyJsonFieldConflicts=!1,o.features=null),null!=e.allowAlias&&e.hasOwnProperty("allowAlias")&&(o.allowAlias=e.allowAlias),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.deprecatedLegacyJsonFieldConflicts&&e.hasOwnProperty("deprecatedLegacyJsonFieldConflicts")&&(o.deprecatedLegacyJsonFieldConflicts=e.deprecatedLegacyJsonFieldConflicts),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},pt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},pt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumOptions"},pt),o.EnumValueOptions=(ut.prototype.deprecated=!1,ut.prototype.features=null,ut.prototype.debugRedact=!1,ut.prototype.uninterpretedOption=a.emptyArray,ut.fromObject=function(e){if(e instanceof s.google.protobuf.EnumValueOptions)return e;var t=new s.google.protobuf.EnumValueOptions;if(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.EnumValueOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(null!=e.debugRedact&&(t.debugRedact=Boolean(e.debugRedact)),e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.EnumValueOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.EnumValueOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},ut.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.deprecated=!1,o.features=null,o.debugRedact=!1),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),null!=e.debugRedact&&e.hasOwnProperty("debugRedact")&&(o.debugRedact=e.debugRedact),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},ut.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ut.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumValueOptions"},ut),o.ServiceOptions=(ct.prototype.features=null,ct.prototype.deprecated=!1,ct.prototype.uninterpretedOption=a.emptyArray,ct.prototype[".google.api.defaultHost"]="",ct.prototype[".google.api.oauthScopes"]="",ct.prototype[".google.api.apiVersion"]="",ct.fromObject=function(e){if(e instanceof s.google.protobuf.ServiceOptions)return e;var t=new s.google.protobuf.ServiceOptions;if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.ServiceOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.ServiceOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.ServiceOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return null!=e[".google.api.defaultHost"]&&(t[".google.api.defaultHost"]=String(e[".google.api.defaultHost"])),null!=e[".google.api.oauthScopes"]&&(t[".google.api.oauthScopes"]=String(e[".google.api.oauthScopes"])),null!=e[".google.api.apiVersion"]&&(t[".google.api.apiVersion"]=String(e[".google.api.apiVersion"])),t},ct.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.deprecated=!1,o.features=null,o[".google.api.defaultHost"]="",o[".google.api.oauthScopes"]="",o[".google.api.apiVersion"]=""),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return null!=e[".google.api.defaultHost"]&&e.hasOwnProperty(".google.api.defaultHost")&&(o[".google.api.defaultHost"]=e[".google.api.defaultHost"]),null!=e[".google.api.oauthScopes"]&&e.hasOwnProperty(".google.api.oauthScopes")&&(o[".google.api.oauthScopes"]=e[".google.api.oauthScopes"]),null!=e[".google.api.apiVersion"]&&e.hasOwnProperty(".google.api.apiVersion")&&(o[".google.api.apiVersion"]=e[".google.api.apiVersion"]),o},ct.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ct.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ServiceOptions"},ct),o.MethodOptions=(C.prototype.deprecated=!1,C.prototype.idempotencyLevel=0,C.prototype.features=null,C.prototype.uninterpretedOption=a.emptyArray,C.prototype[".google.api.http"]=null,C.prototype[".google.api.methodSignature"]=a.emptyArray,C.prototype[".google.longrunning.operationInfo"]=null,C.fromObject=function(e){if(e instanceof s.google.protobuf.MethodOptions)return e;var t=new s.google.protobuf.MethodOptions;switch(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),e.idempotencyLevel){default:"number"==typeof e.idempotencyLevel&&(t.idempotencyLevel=e.idempotencyLevel);break;case"IDEMPOTENCY_UNKNOWN":case 0:t.idempotencyLevel=0;break;case"NO_SIDE_EFFECTS":case 1:t.idempotencyLevel=1;break;case"IDEMPOTENT":case 2:t.idempotencyLevel=2}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.MethodOptions.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.MethodOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.MethodOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=s.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(null!=e[".google.api.http"]){if("object"!=typeof e[".google.api.http"])throw TypeError(".google.protobuf.MethodOptions..google.api.http: object expected");t[".google.api.http"]=s.google.api.HttpRule.fromObject(e[".google.api.http"])}if(e[".google.api.methodSignature"]){if(!Array.isArray(e[".google.api.methodSignature"]))throw TypeError(".google.protobuf.MethodOptions..google.api.methodSignature: array expected");t[".google.api.methodSignature"]=[];for(o=0;o<e[".google.api.methodSignature"].length;++o)t[".google.api.methodSignature"][o]=String(e[".google.api.methodSignature"][o])}if(null!=e[".google.longrunning.operationInfo"]){if("object"!=typeof e[".google.longrunning.operationInfo"])throw TypeError(".google.protobuf.MethodOptions..google.longrunning.operationInfo: object expected");t[".google.longrunning.operationInfo"]=s.google.longrunning.OperationInfo.fromObject(e[".google.longrunning.operationInfo"])}return t},C.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[],o[".google.api.methodSignature"]=[]),t.defaults&&(o.deprecated=!1,o.idempotencyLevel=t.enums===String?"IDEMPOTENCY_UNKNOWN":0,o.features=null,o[".google.longrunning.operationInfo"]=null,o[".google.api.http"]=null),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.idempotencyLevel&&e.hasOwnProperty("idempotencyLevel")&&(o.idempotencyLevel=t.enums!==String||void 0===s.google.protobuf.MethodOptions.IdempotencyLevel[e.idempotencyLevel]?e.idempotencyLevel:s.google.protobuf.MethodOptions.IdempotencyLevel[e.idempotencyLevel]),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=s.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(null!=e[".google.longrunning.operationInfo"]&&e.hasOwnProperty(".google.longrunning.operationInfo")&&(o[".google.longrunning.operationInfo"]=s.google.longrunning.OperationInfo.toObject(e[".google.longrunning.operationInfo"],t)),e[".google.api.methodSignature"]&&e[".google.api.methodSignature"].length){o[".google.api.methodSignature"]=[];for(r=0;r<e[".google.api.methodSignature"].length;++r)o[".google.api.methodSignature"][r]=e[".google.api.methodSignature"][r]}return null!=e[".google.api.http"]&&e.hasOwnProperty(".google.api.http")&&(o[".google.api.http"]=s.google.api.HttpRule.toObject(e[".google.api.http"],t)),o},C.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},C.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MethodOptions"},C.IdempotencyLevel=(n={},(e=Object.create(n))[n[0]="IDEMPOTENCY_UNKNOWN"]="IDEMPOTENCY_UNKNOWN",e[n[1]="NO_SIDE_EFFECTS"]="NO_SIDE_EFFECTS",e[n[2]="IDEMPOTENT"]="IDEMPOTENT",e),C),o.UninterpretedOption=(x.prototype.name=a.emptyArray,x.prototype.identifierValue="",x.prototype.positiveIntValue=a.Long?a.Long.fromBits(0,0,!0):0,x.prototype.negativeIntValue=a.Long?a.Long.fromBits(0,0,!1):0,x.prototype.doubleValue=0,x.prototype.stringValue=a.newBuffer([]),x.prototype.aggregateValue="",x.fromObject=function(e){if(e instanceof s.google.protobuf.UninterpretedOption)return e;var t=new s.google.protobuf.UninterpretedOption;if(e.name){if(!Array.isArray(e.name))throw TypeError(".google.protobuf.UninterpretedOption.name: array expected");t.name=[];for(var o=0;o<e.name.length;++o){if("object"!=typeof e.name[o])throw TypeError(".google.protobuf.UninterpretedOption.name: object expected");t.name[o]=s.google.protobuf.UninterpretedOption.NamePart.fromObject(e.name[o])}}return null!=e.identifierValue&&(t.identifierValue=String(e.identifierValue)),null!=e.positiveIntValue&&(a.Long?(t.positiveIntValue=a.Long.fromValue(e.positiveIntValue)).unsigned=!0:"string"==typeof e.positiveIntValue?t.positiveIntValue=parseInt(e.positiveIntValue,10):"number"==typeof e.positiveIntValue?t.positiveIntValue=e.positiveIntValue:"object"==typeof e.positiveIntValue&&(t.positiveIntValue=new a.LongBits(e.positiveIntValue.low>>>0,e.positiveIntValue.high>>>0).toNumber(!0))),null!=e.negativeIntValue&&(a.Long?(t.negativeIntValue=a.Long.fromValue(e.negativeIntValue)).unsigned=!1:"string"==typeof e.negativeIntValue?t.negativeIntValue=parseInt(e.negativeIntValue,10):"number"==typeof e.negativeIntValue?t.negativeIntValue=e.negativeIntValue:"object"==typeof e.negativeIntValue&&(t.negativeIntValue=new a.LongBits(e.negativeIntValue.low>>>0,e.negativeIntValue.high>>>0).toNumber())),null!=e.doubleValue&&(t.doubleValue=Number(e.doubleValue)),null!=e.stringValue&&("string"==typeof e.stringValue?a.base64.decode(e.stringValue,t.stringValue=a.newBuffer(a.base64.length(e.stringValue)),0):0<=e.stringValue.length&&(t.stringValue=e.stringValue)),null!=e.aggregateValue&&(t.aggregateValue=String(e.aggregateValue)),t},x.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.name=[]),t.defaults&&(r.identifierValue="",a.Long?(o=new a.Long(0,0,!0),r.positiveIntValue=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.positiveIntValue=t.longs===String?"0":0,a.Long?(o=new a.Long(0,0,!1),r.negativeIntValue=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.negativeIntValue=t.longs===String?"0":0,r.doubleValue=0,t.bytes===String?r.stringValue="":(r.stringValue=[],t.bytes!==Array&&(r.stringValue=a.newBuffer(r.stringValue))),r.aggregateValue=""),e.name&&e.name.length){r.name=[];for(var n=0;n<e.name.length;++n)r.name[n]=s.google.protobuf.UninterpretedOption.NamePart.toObject(e.name[n],t)}return null!=e.identifierValue&&e.hasOwnProperty("identifierValue")&&(r.identifierValue=e.identifierValue),null!=e.positiveIntValue&&e.hasOwnProperty("positiveIntValue")&&("number"==typeof e.positiveIntValue?r.positiveIntValue=t.longs===String?String(e.positiveIntValue):e.positiveIntValue:r.positiveIntValue=t.longs===String?a.Long.prototype.toString.call(e.positiveIntValue):t.longs===Number?new a.LongBits(e.positiveIntValue.low>>>0,e.positiveIntValue.high>>>0).toNumber(!0):e.positiveIntValue),null!=e.negativeIntValue&&e.hasOwnProperty("negativeIntValue")&&("number"==typeof e.negativeIntValue?r.negativeIntValue=t.longs===String?String(e.negativeIntValue):e.negativeIntValue:r.negativeIntValue=t.longs===String?a.Long.prototype.toString.call(e.negativeIntValue):t.longs===Number?new a.LongBits(e.negativeIntValue.low>>>0,e.negativeIntValue.high>>>0).toNumber():e.negativeIntValue),null!=e.doubleValue&&e.hasOwnProperty("doubleValue")&&(r.doubleValue=t.json&&!isFinite(e.doubleValue)?String(e.doubleValue):e.doubleValue),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(r.stringValue=t.bytes===String?a.base64.encode(e.stringValue,0,e.stringValue.length):t.bytes===Array?Array.prototype.slice.call(e.stringValue):e.stringValue),null!=e.aggregateValue&&e.hasOwnProperty("aggregateValue")&&(r.aggregateValue=e.aggregateValue),r},x.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},x.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UninterpretedOption"},x.NamePart=(gt.prototype.namePart="",gt.prototype.isExtension=!1,gt.fromObject=function(e){var t;return e instanceof s.google.protobuf.UninterpretedOption.NamePart?e:(t=new s.google.protobuf.UninterpretedOption.NamePart,null!=e.namePart&&(t.namePart=String(e.namePart)),null!=e.isExtension&&(t.isExtension=Boolean(e.isExtension)),t)},gt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.namePart="",o.isExtension=!1),null!=e.namePart&&e.hasOwnProperty("namePart")&&(o.namePart=e.namePart),null!=e.isExtension&&e.hasOwnProperty("isExtension")&&(o.isExtension=e.isExtension),o},gt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},gt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UninterpretedOption.NamePart"},gt),x),o.FeatureSet=(A.prototype.fieldPresence=0,A.prototype.enumType=0,A.prototype.repeatedFieldEncoding=0,A.prototype.utf8Validation=0,A.prototype.messageEncoding=0,A.prototype.jsonFormat=0,A.fromObject=function(e){if(e instanceof s.google.protobuf.FeatureSet)return e;var t=new s.google.protobuf.FeatureSet;switch(e.fieldPresence){default:"number"==typeof e.fieldPresence&&(t.fieldPresence=e.fieldPresence);break;case"FIELD_PRESENCE_UNKNOWN":case 0:t.fieldPresence=0;break;case"EXPLICIT":case 1:t.fieldPresence=1;break;case"IMPLICIT":case 2:t.fieldPresence=2;break;case"LEGACY_REQUIRED":case 3:t.fieldPresence=3}switch(e.enumType){default:"number"==typeof e.enumType&&(t.enumType=e.enumType);break;case"ENUM_TYPE_UNKNOWN":case 0:t.enumType=0;break;case"OPEN":case 1:t.enumType=1;break;case"CLOSED":case 2:t.enumType=2}switch(e.repeatedFieldEncoding){default:"number"==typeof e.repeatedFieldEncoding&&(t.repeatedFieldEncoding=e.repeatedFieldEncoding);break;case"REPEATED_FIELD_ENCODING_UNKNOWN":case 0:t.repeatedFieldEncoding=0;break;case"PACKED":case 1:t.repeatedFieldEncoding=1;break;case"EXPANDED":case 2:t.repeatedFieldEncoding=2}switch(e.utf8Validation){default:"number"==typeof e.utf8Validation&&(t.utf8Validation=e.utf8Validation);break;case"UTF8_VALIDATION_UNKNOWN":case 0:t.utf8Validation=0;break;case"VERIFY":case 2:t.utf8Validation=2;break;case"NONE":case 3:t.utf8Validation=3}switch(e.messageEncoding){default:"number"==typeof e.messageEncoding&&(t.messageEncoding=e.messageEncoding);break;case"MESSAGE_ENCODING_UNKNOWN":case 0:t.messageEncoding=0;break;case"LENGTH_PREFIXED":case 1:t.messageEncoding=1;break;case"DELIMITED":case 2:t.messageEncoding=2}switch(e.jsonFormat){default:"number"==typeof e.jsonFormat&&(t.jsonFormat=e.jsonFormat);break;case"JSON_FORMAT_UNKNOWN":case 0:t.jsonFormat=0;break;case"ALLOW":case 1:t.jsonFormat=1;break;case"LEGACY_BEST_EFFORT":case 2:t.jsonFormat=2}return t},A.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPresence=t.enums===String?"FIELD_PRESENCE_UNKNOWN":0,o.enumType=t.enums===String?"ENUM_TYPE_UNKNOWN":0,o.repeatedFieldEncoding=t.enums===String?"REPEATED_FIELD_ENCODING_UNKNOWN":0,o.utf8Validation=t.enums===String?"UTF8_VALIDATION_UNKNOWN":0,o.messageEncoding=t.enums===String?"MESSAGE_ENCODING_UNKNOWN":0,o.jsonFormat=t.enums===String?"JSON_FORMAT_UNKNOWN":0),null!=e.fieldPresence&&e.hasOwnProperty("fieldPresence")&&(o.fieldPresence=t.enums!==String||void 0===s.google.protobuf.FeatureSet.FieldPresence[e.fieldPresence]?e.fieldPresence:s.google.protobuf.FeatureSet.FieldPresence[e.fieldPresence]),null!=e.enumType&&e.hasOwnProperty("enumType")&&(o.enumType=t.enums!==String||void 0===s.google.protobuf.FeatureSet.EnumType[e.enumType]?e.enumType:s.google.protobuf.FeatureSet.EnumType[e.enumType]),null!=e.repeatedFieldEncoding&&e.hasOwnProperty("repeatedFieldEncoding")&&(o.repeatedFieldEncoding=t.enums!==String||void 0===s.google.protobuf.FeatureSet.RepeatedFieldEncoding[e.repeatedFieldEncoding]?e.repeatedFieldEncoding:s.google.protobuf.FeatureSet.RepeatedFieldEncoding[e.repeatedFieldEncoding]),null!=e.utf8Validation&&e.hasOwnProperty("utf8Validation")&&(o.utf8Validation=t.enums!==String||void 0===s.google.protobuf.FeatureSet.Utf8Validation[e.utf8Validation]?e.utf8Validation:s.google.protobuf.FeatureSet.Utf8Validation[e.utf8Validation]),null!=e.messageEncoding&&e.hasOwnProperty("messageEncoding")&&(o.messageEncoding=t.enums!==String||void 0===s.google.protobuf.FeatureSet.MessageEncoding[e.messageEncoding]?e.messageEncoding:s.google.protobuf.FeatureSet.MessageEncoding[e.messageEncoding]),null!=e.jsonFormat&&e.hasOwnProperty("jsonFormat")&&(o.jsonFormat=t.enums!==String||void 0===s.google.protobuf.FeatureSet.JsonFormat[e.jsonFormat]?e.jsonFormat:s.google.protobuf.FeatureSet.JsonFormat[e.jsonFormat]),o},A.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},A.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSet"},A.FieldPresence=(n={},(e=Object.create(n))[n[0]="FIELD_PRESENCE_UNKNOWN"]="FIELD_PRESENCE_UNKNOWN",e[n[1]="EXPLICIT"]="EXPLICIT",e[n[2]="IMPLICIT"]="IMPLICIT",e[n[3]="LEGACY_REQUIRED"]="LEGACY_REQUIRED",e),A.EnumType=(n={},(e=Object.create(n))[n[0]="ENUM_TYPE_UNKNOWN"]="ENUM_TYPE_UNKNOWN",e[n[1]="OPEN"]="OPEN",e[n[2]="CLOSED"]="CLOSED",e),A.RepeatedFieldEncoding=(n={},(e=Object.create(n))[n[0]="REPEATED_FIELD_ENCODING_UNKNOWN"]="REPEATED_FIELD_ENCODING_UNKNOWN",e[n[1]="PACKED"]="PACKED",e[n[2]="EXPANDED"]="EXPANDED",e),A.Utf8Validation=(n={},(e=Object.create(n))[n[0]="UTF8_VALIDATION_UNKNOWN"]="UTF8_VALIDATION_UNKNOWN",e[n[2]="VERIFY"]="VERIFY",e[n[3]="NONE"]="NONE",e),A.MessageEncoding=(n={},(e=Object.create(n))[n[0]="MESSAGE_ENCODING_UNKNOWN"]="MESSAGE_ENCODING_UNKNOWN",e[n[1]="LENGTH_PREFIXED"]="LENGTH_PREFIXED",e[n[2]="DELIMITED"]="DELIMITED",e),A.JsonFormat=(n={},(e=Object.create(n))[n[0]="JSON_FORMAT_UNKNOWN"]="JSON_FORMAT_UNKNOWN",e[n[1]="ALLOW"]="ALLOW",e[n[2]="LEGACY_BEST_EFFORT"]="LEGACY_BEST_EFFORT",e),A),o.FeatureSetDefaults=(ft.prototype.defaults=a.emptyArray,ft.prototype.minimumEdition=0,ft.prototype.maximumEdition=0,ft.fromObject=function(e){if(e instanceof s.google.protobuf.FeatureSetDefaults)return e;var t=new s.google.protobuf.FeatureSetDefaults;if(e.defaults){if(!Array.isArray(e.defaults))throw TypeError(".google.protobuf.FeatureSetDefaults.defaults: array expected");t.defaults=[];for(var o=0;o<e.defaults.length;++o){if("object"!=typeof e.defaults[o])throw TypeError(".google.protobuf.FeatureSetDefaults.defaults: object expected");t.defaults[o]=s.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.fromObject(e.defaults[o])}}switch(e.minimumEdition){default:"number"==typeof e.minimumEdition&&(t.minimumEdition=e.minimumEdition);break;case"EDITION_UNKNOWN":case 0:t.minimumEdition=0;break;case"EDITION_PROTO2":case 998:t.minimumEdition=998;break;case"EDITION_PROTO3":case 999:t.minimumEdition=999;break;case"EDITION_2023":case 1e3:t.minimumEdition=1e3;break;case"EDITION_2024":case 1001:t.minimumEdition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.minimumEdition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.minimumEdition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.minimumEdition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.minimumEdition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.minimumEdition=99999;break;case"EDITION_MAX":case 2147483647:t.minimumEdition=2147483647}switch(e.maximumEdition){default:"number"==typeof e.maximumEdition&&(t.maximumEdition=e.maximumEdition);break;case"EDITION_UNKNOWN":case 0:t.maximumEdition=0;break;case"EDITION_PROTO2":case 998:t.maximumEdition=998;break;case"EDITION_PROTO3":case 999:t.maximumEdition=999;break;case"EDITION_2023":case 1e3:t.maximumEdition=1e3;break;case"EDITION_2024":case 1001:t.maximumEdition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.maximumEdition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.maximumEdition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.maximumEdition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.maximumEdition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.maximumEdition=99999;break;case"EDITION_MAX":case 2147483647:t.maximumEdition=2147483647}return t},ft.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.defaults=[]),t.defaults&&(o.minimumEdition=t.enums===String?"EDITION_UNKNOWN":0,o.maximumEdition=t.enums===String?"EDITION_UNKNOWN":0),e.defaults&&e.defaults.length){o.defaults=[];for(var r=0;r<e.defaults.length;++r)o.defaults[r]=s.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.toObject(e.defaults[r],t)}return null!=e.minimumEdition&&e.hasOwnProperty("minimumEdition")&&(o.minimumEdition=t.enums!==String||void 0===s.google.protobuf.Edition[e.minimumEdition]?e.minimumEdition:s.google.protobuf.Edition[e.minimumEdition]),null!=e.maximumEdition&&e.hasOwnProperty("maximumEdition")&&(o.maximumEdition=t.enums!==String||void 0===s.google.protobuf.Edition[e.maximumEdition]?e.maximumEdition:s.google.protobuf.Edition[e.maximumEdition]),o},ft.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ft.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSetDefaults"},ft.FeatureSetEditionDefault=(dt.prototype.edition=0,dt.prototype.features=null,dt.fromObject=function(e){if(e instanceof s.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault)return e;var t=new s.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;switch(e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.features: object expected");t.features=s.google.protobuf.FeatureSet.fromObject(e.features)}return t},dt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.features=null,o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.features&&e.hasOwnProperty("features")&&(o.features=s.google.protobuf.FeatureSet.toObject(e.features,t)),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===s.google.protobuf.Edition[e.edition]?e.edition:s.google.protobuf.Edition[e.edition]),o},dt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},dt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},dt),ft),o.SourceCodeInfo=(yt.prototype.location=a.emptyArray,yt.fromObject=function(e){if(e instanceof s.google.protobuf.SourceCodeInfo)return e;var t=new s.google.protobuf.SourceCodeInfo;if(e.location){if(!Array.isArray(e.location))throw TypeError(".google.protobuf.SourceCodeInfo.location: array expected");t.location=[];for(var o=0;o<e.location.length;++o){if("object"!=typeof e.location[o])throw TypeError(".google.protobuf.SourceCodeInfo.location: object expected");t.location[o]=s.google.protobuf.SourceCodeInfo.Location.fromObject(e.location[o])}}return t},yt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.location=[]),e.location&&e.location.length){o.location=[];for(var r=0;r<e.location.length;++r)o.location[r]=s.google.protobuf.SourceCodeInfo.Location.toObject(e.location[r],t)}return o},yt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},yt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.SourceCodeInfo"},yt.Location=(mt.prototype.path=a.emptyArray,mt.prototype.span=a.emptyArray,mt.prototype.leadingComments="",mt.prototype.trailingComments="",mt.prototype.leadingDetachedComments=a.emptyArray,mt.fromObject=function(e){if(e instanceof s.google.protobuf.SourceCodeInfo.Location)return e;var t=new s.google.protobuf.SourceCodeInfo.Location;if(e.path){if(!Array.isArray(e.path))throw TypeError(".google.protobuf.SourceCodeInfo.Location.path: array expected");t.path=[];for(var o=0;o<e.path.length;++o)t.path[o]=0|e.path[o]}if(e.span){if(!Array.isArray(e.span))throw TypeError(".google.protobuf.SourceCodeInfo.Location.span: array expected");t.span=[];for(o=0;o<e.span.length;++o)t.span[o]=0|e.span[o]}if(null!=e.leadingComments&&(t.leadingComments=String(e.leadingComments)),null!=e.trailingComments&&(t.trailingComments=String(e.trailingComments)),e.leadingDetachedComments){if(!Array.isArray(e.leadingDetachedComments))throw TypeError(".google.protobuf.SourceCodeInfo.Location.leadingDetachedComments: array expected");t.leadingDetachedComments=[];for(o=0;o<e.leadingDetachedComments.length;++o)t.leadingDetachedComments[o]=String(e.leadingDetachedComments[o])}return t},mt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.path=[],o.span=[],o.leadingDetachedComments=[]),t.defaults&&(o.leadingComments="",o.trailingComments=""),e.path&&e.path.length){o.path=[];for(var r=0;r<e.path.length;++r)o.path[r]=e.path[r]}if(e.span&&e.span.length){o.span=[];for(r=0;r<e.span.length;++r)o.span[r]=e.span[r]}if(null!=e.leadingComments&&e.hasOwnProperty("leadingComments")&&(o.leadingComments=e.leadingComments),null!=e.trailingComments&&e.hasOwnProperty("trailingComments")&&(o.trailingComments=e.trailingComments),e.leadingDetachedComments&&e.leadingDetachedComments.length){o.leadingDetachedComments=[];for(r=0;r<e.leadingDetachedComments.length;++r)o.leadingDetachedComments[r]=e.leadingDetachedComments[r]}return o},mt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},mt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.SourceCodeInfo.Location"},mt),yt),o.GeneratedCodeInfo=(bt.prototype.annotation=a.emptyArray,bt.fromObject=function(e){if(e instanceof s.google.protobuf.GeneratedCodeInfo)return e;var t=new s.google.protobuf.GeneratedCodeInfo;if(e.annotation){if(!Array.isArray(e.annotation))throw TypeError(".google.protobuf.GeneratedCodeInfo.annotation: array expected");t.annotation=[];for(var o=0;o<e.annotation.length;++o){if("object"!=typeof e.annotation[o])throw TypeError(".google.protobuf.GeneratedCodeInfo.annotation: object expected");t.annotation[o]=s.google.protobuf.GeneratedCodeInfo.Annotation.fromObject(e.annotation[o])}}return t},bt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.annotation=[]),e.annotation&&e.annotation.length){o.annotation=[];for(var r=0;r<e.annotation.length;++r)o.annotation[r]=s.google.protobuf.GeneratedCodeInfo.Annotation.toObject(e.annotation[r],t)}return o},bt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},bt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.GeneratedCodeInfo"},bt.Annotation=(Ot.prototype.path=a.emptyArray,Ot.prototype.sourceFile="",Ot.prototype.begin=0,Ot.prototype.end=0,Ot.prototype.semantic=0,Ot.fromObject=function(e){if(e instanceof s.google.protobuf.GeneratedCodeInfo.Annotation)return e;var t=new s.google.protobuf.GeneratedCodeInfo.Annotation;if(e.path){if(!Array.isArray(e.path))throw TypeError(".google.protobuf.GeneratedCodeInfo.Annotation.path: array expected");t.path=[];for(var o=0;o<e.path.length;++o)t.path[o]=0|e.path[o]}switch(null!=e.sourceFile&&(t.sourceFile=String(e.sourceFile)),null!=e.begin&&(t.begin=0|e.begin),null!=e.end&&(t.end=0|e.end),e.semantic){default:"number"==typeof e.semantic&&(t.semantic=e.semantic);break;case"NONE":case 0:t.semantic=0;break;case"SET":case 1:t.semantic=1;break;case"ALIAS":case 2:t.semantic=2}return t},Ot.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.path=[]),t.defaults&&(o.sourceFile="",o.begin=0,o.end=0,o.semantic=t.enums===String?"NONE":0),e.path&&e.path.length){o.path=[];for(var r=0;r<e.path.length;++r)o.path[r]=e.path[r]}return null!=e.sourceFile&&e.hasOwnProperty("sourceFile")&&(o.sourceFile=e.sourceFile),null!=e.begin&&e.hasOwnProperty("begin")&&(o.begin=e.begin),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),null!=e.semantic&&e.hasOwnProperty("semantic")&&(o.semantic=t.enums!==String||void 0===s.google.protobuf.GeneratedCodeInfo.Annotation.Semantic[e.semantic]?e.semantic:s.google.protobuf.GeneratedCodeInfo.Annotation.Semantic[e.semantic]),o},Ot.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ot.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.GeneratedCodeInfo.Annotation"},Ot.Semantic=(n={},(e=Object.create(n))[n[0]="NONE"]="NONE",e[n[1]="SET"]="SET",e[n[2]="ALIAS"]="ALIAS",e),Ot),bt),o.Timestamp=(ht.prototype.seconds=a.Long?a.Long.fromBits(0,0,!1):0,ht.prototype.nanos=0,ht.fromObject=function(e){var t;return e instanceof s.google.protobuf.Timestamp?e:(t=new s.google.protobuf.Timestamp,null!=e.seconds&&(a.Long?(t.seconds=a.Long.fromValue(e.seconds)).unsigned=!1:"string"==typeof e.seconds?t.seconds=parseInt(e.seconds,10):"number"==typeof e.seconds?t.seconds=e.seconds:"object"==typeof e.seconds&&(t.seconds=new a.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber())),null!=e.nanos&&(t.nanos=0|e.nanos),t)},ht.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(a.Long?(o=new a.Long(0,0,!1),r.seconds=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.seconds=t.longs===String?"0":0,r.nanos=0),null!=e.seconds&&e.hasOwnProperty("seconds")&&("number"==typeof e.seconds?r.seconds=t.longs===String?String(e.seconds):e.seconds:r.seconds=t.longs===String?a.Long.prototype.toString.call(e.seconds):t.longs===Number?new a.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber():e.seconds),null!=e.nanos&&e.hasOwnProperty("nanos")&&(r.nanos=e.nanos),r},ht.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ht.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Timestamp"},ht),o.Duration=(St.prototype.seconds=a.Long?a.Long.fromBits(0,0,!1):0,St.prototype.nanos=0,St.fromObject=function(e){var t;return e instanceof s.google.protobuf.Duration?e:(t=new s.google.protobuf.Duration,null!=e.seconds&&(a.Long?(t.seconds=a.Long.fromValue(e.seconds)).unsigned=!1:"string"==typeof e.seconds?t.seconds=parseInt(e.seconds,10):"number"==typeof e.seconds?t.seconds=e.seconds:"object"==typeof e.seconds&&(t.seconds=new a.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber())),null!=e.nanos&&(t.nanos=0|e.nanos),t)},St.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(a.Long?(o=new a.Long(0,0,!1),r.seconds=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.seconds=t.longs===String?"0":0,r.nanos=0),null!=e.seconds&&e.hasOwnProperty("seconds")&&("number"==typeof e.seconds?r.seconds=t.longs===String?String(e.seconds):e.seconds:r.seconds=t.longs===String?a.Long.prototype.toString.call(e.seconds):t.longs===Number?new a.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber():e.seconds),null!=e.nanos&&e.hasOwnProperty("nanos")&&(r.nanos=e.nanos),r},St.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},St.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Duration"},St),o.Any=(vt.prototype.type_url="",vt.prototype.value=a.newBuffer([]),vt.fromObject=function(e){var t;return e instanceof s.google.protobuf.Any?e:(t=new s.google.protobuf.Any,null!=e.type_url&&(t.type_url=String(e.type_url)),null!=e.value&&("string"==typeof e.value?a.base64.decode(e.value,t.value=a.newBuffer(a.base64.length(e.value)),0):0<=e.value.length&&(t.value=e.value)),t)},vt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.type_url="",t.bytes===String?o.value="":(o.value=[],t.bytes!==Array&&(o.value=a.newBuffer(o.value)))),null!=e.type_url&&e.hasOwnProperty("type_url")&&(o.type_url=e.type_url),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.bytes===String?a.base64.encode(e.value,0,e.value.length):t.bytes===Array?Array.prototype.slice.call(e.value):e.value),o},vt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},vt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Any"},vt),o.Empty=(Et.fromObject=function(e){return e instanceof s.google.protobuf.Empty?e:new s.google.protobuf.Empty},Et.toObject=function(){return{}},Et.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Et.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Empty"},Et),o.FieldMask=(Tt.prototype.paths=a.emptyArray,Tt.fromObject=function(e){if(e instanceof s.google.protobuf.FieldMask)return e;var t=new s.google.protobuf.FieldMask;if(e.paths){if(!Array.isArray(e.paths))throw TypeError(".google.protobuf.FieldMask.paths: array expected");t.paths=[];for(var o=0;o<e.paths.length;++o)t.paths[o]=String(e.paths[o])}return t},Tt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.paths=[]),e.paths&&e.paths.length){o.paths=[];for(var r=0;r<e.paths.length;++r)o.paths[r]=e.paths[r]}return o},Tt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Tt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldMask"},Tt),o.Struct=(It.prototype.fields=a.emptyObject,It.fromObject=function(e){if(e instanceof s.google.protobuf.Struct)return e;var t=new s.google.protobuf.Struct;if(e.fields){if("object"!=typeof e.fields)throw TypeError(".google.protobuf.Struct.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.protobuf.Struct.fields: object expected");t.fields[o[r]]=s.google.protobuf.Value.fromObject(e.fields[o[r]])}}return t},It.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=s.google.protobuf.Value.toObject(e.fields[o[n]],t)}return r},It.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},It.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Struct"},It),o.Value=(_.prototype.nullValue=null,_.prototype.numberValue=null,_.prototype.stringValue=null,_.prototype.boolValue=null,_.prototype.structValue=null,_.prototype.listValue=null,Object.defineProperty(_.prototype,"kind",{get:a.oneOfGetter(n=["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]),set:a.oneOfSetter(n)}),_.fromObject=function(e){if(e instanceof s.google.protobuf.Value)return e;var t=new s.google.protobuf.Value;switch(e.nullValue){default:"number"==typeof e.nullValue&&(t.nullValue=e.nullValue);break;case"NULL_VALUE":case 0:t.nullValue=0}if(null!=e.numberValue&&(t.numberValue=Number(e.numberValue)),null!=e.stringValue&&(t.stringValue=String(e.stringValue)),null!=e.boolValue&&(t.boolValue=Boolean(e.boolValue)),null!=e.structValue){if("object"!=typeof e.structValue)throw TypeError(".google.protobuf.Value.structValue: object expected");t.structValue=s.google.protobuf.Struct.fromObject(e.structValue)}if(null!=e.listValue){if("object"!=typeof e.listValue)throw TypeError(".google.protobuf.Value.listValue: object expected");t.listValue=s.google.protobuf.ListValue.fromObject(e.listValue)}return t},_.toObject=function(e,t){t=t||{};var o={};return null!=e.nullValue&&e.hasOwnProperty("nullValue")&&(o.nullValue=t.enums!==String||void 0===s.google.protobuf.NullValue[e.nullValue]?e.nullValue:s.google.protobuf.NullValue[e.nullValue],t.oneofs)&&(o.kind="nullValue"),null!=e.numberValue&&e.hasOwnProperty("numberValue")&&(o.numberValue=t.json&&!isFinite(e.numberValue)?String(e.numberValue):e.numberValue,t.oneofs)&&(o.kind="numberValue"),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(o.stringValue=e.stringValue,t.oneofs)&&(o.kind="stringValue"),null!=e.boolValue&&e.hasOwnProperty("boolValue")&&(o.boolValue=e.boolValue,t.oneofs)&&(o.kind="boolValue"),null!=e.structValue&&e.hasOwnProperty("structValue")&&(o.structValue=s.google.protobuf.Struct.toObject(e.structValue,t),t.oneofs)&&(o.kind="structValue"),null!=e.listValue&&e.hasOwnProperty("listValue")&&(o.listValue=s.google.protobuf.ListValue.toObject(e.listValue,t),t.oneofs)&&(o.kind="listValue"),o},_.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Value"},_),o.NullValue=(e={},(n=Object.create(e))[e[0]="NULL_VALUE"]="NULL_VALUE",n),o.ListValue=(jt.prototype.values=a.emptyArray,jt.fromObject=function(e){if(e instanceof s.google.protobuf.ListValue)return e;var t=new s.google.protobuf.ListValue;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.protobuf.ListValue.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.protobuf.ListValue.values: object expected");t.values[o]=s.google.protobuf.Value.fromObject(e.values[o])}}return t},jt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=s.google.protobuf.Value.toObject(e.values[r],t)}return o},jt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},jt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ListValue"},jt),o.DoubleValue=(Nt.prototype.value=0,Nt.fromObject=function(e){var t;return e instanceof s.google.protobuf.DoubleValue?e:(t=new s.google.protobuf.DoubleValue,null!=e.value&&(t.value=Number(e.value)),t)},Nt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.json&&!isFinite(e.value)?String(e.value):e.value),o},Nt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Nt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DoubleValue"},Nt),o.FloatValue=(Pt.prototype.value=0,Pt.fromObject=function(e){var t;return e instanceof s.google.protobuf.FloatValue?e:(t=new s.google.protobuf.FloatValue,null!=e.value&&(t.value=Number(e.value)),t)},Pt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.json&&!isFinite(e.value)?String(e.value):e.value),o},Pt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Pt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FloatValue"},Pt),o.Int64Value=(Dt.prototype.value=a.Long?a.Long.fromBits(0,0,!1):0,Dt.fromObject=function(e){var t;return e instanceof s.google.protobuf.Int64Value?e:(t=new s.google.protobuf.Int64Value,null!=e.value&&(a.Long?(t.value=a.Long.fromValue(e.value)).unsigned=!1:"string"==typeof e.value?t.value=parseInt(e.value,10):"number"==typeof e.value?t.value=e.value:"object"==typeof e.value&&(t.value=new a.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber())),t)},Dt.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(a.Long?(o=new a.Long(0,0,!1),r.value=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.value=t.longs===String?"0":0),null!=e.value&&e.hasOwnProperty("value")&&("number"==typeof e.value?r.value=t.longs===String?String(e.value):e.value:r.value=t.longs===String?a.Long.prototype.toString.call(e.value):t.longs===Number?new a.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber():e.value),r},Dt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Dt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Int64Value"},Dt),o.UInt64Value=(wt.prototype.value=a.Long?a.Long.fromBits(0,0,!0):0,wt.fromObject=function(e){var t;return e instanceof s.google.protobuf.UInt64Value?e:(t=new s.google.protobuf.UInt64Value,null!=e.value&&(a.Long?(t.value=a.Long.fromValue(e.value)).unsigned=!0:"string"==typeof e.value?t.value=parseInt(e.value,10):"number"==typeof e.value?t.value=e.value:"object"==typeof e.value&&(t.value=new a.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber(!0))),t)},wt.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(a.Long?(o=new a.Long(0,0,!0),r.value=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.value=t.longs===String?"0":0),null!=e.value&&e.hasOwnProperty("value")&&("number"==typeof e.value?r.value=t.longs===String?String(e.value):e.value:r.value=t.longs===String?a.Long.prototype.toString.call(e.value):t.longs===Number?new a.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber(!0):e.value),r},wt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},wt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UInt64Value"},wt),o.Int32Value=(kt.prototype.value=0,kt.fromObject=function(e){var t;return e instanceof s.google.protobuf.Int32Value?e:(t=new s.google.protobuf.Int32Value,null!=e.value&&(t.value=0|e.value),t)},kt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},kt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},kt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Int32Value"},kt),o.UInt32Value=(Rt.prototype.value=0,Rt.fromObject=function(e){var t;return e instanceof s.google.protobuf.UInt32Value?e:(t=new s.google.protobuf.UInt32Value,null!=e.value&&(t.value=e.value>>>0),t)},Rt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},Rt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Rt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UInt32Value"},Rt),o.BoolValue=(Ct.prototype.value=!1,Ct.fromObject=function(e){var t;return e instanceof s.google.protobuf.BoolValue?e:(t=new s.google.protobuf.BoolValue,null!=e.value&&(t.value=Boolean(e.value)),t)},Ct.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=!1),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},Ct.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ct.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.BoolValue"},Ct),o.StringValue=(xt.prototype.value="",xt.fromObject=function(e){var t;return e instanceof s.google.protobuf.StringValue?e:(t=new s.google.protobuf.StringValue,null!=e.value&&(t.value=String(e.value)),t)},xt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=""),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},xt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},xt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.StringValue"},xt),o.BytesValue=(At.prototype.value=a.newBuffer([]),At.fromObject=function(e){var t;return e instanceof s.google.protobuf.BytesValue?e:(t=new s.google.protobuf.BytesValue,null!=e.value&&("string"==typeof e.value?a.base64.decode(e.value,t.value=a.newBuffer(a.base64.length(e.value)),0):0<=e.value.length&&(t.value=e.value)),t)},At.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.value="":(o.value=[],t.bytes!==Array&&(o.value=a.newBuffer(o.value)))),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.bytes===String?a.base64.encode(e.value,0,e.value.length):t.bytes===Array?Array.prototype.slice.call(e.value):e.value),o},At.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},At.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.BytesValue"},At),o),L.type=((t={}).DayOfWeek=(i={},(e=Object.create(i))[i[0]="DAY_OF_WEEK_UNSPECIFIED"]="DAY_OF_WEEK_UNSPECIFIED",e[i[1]="MONDAY"]="MONDAY",e[i[2]="TUESDAY"]="TUESDAY",e[i[3]="WEDNESDAY"]="WEDNESDAY",e[i[4]="THURSDAY"]="THURSDAY",e[i[5]="FRIDAY"]="FRIDAY",e[i[6]="SATURDAY"]="SATURDAY",e[i[7]="SUNDAY"]="SUNDAY",e),t.LatLng=(_t.prototype.latitude=0,_t.prototype.longitude=0,_t.fromObject=function(e){var t;return e instanceof s.google.type.LatLng?e:(t=new s.google.type.LatLng,null!=e.latitude&&(t.latitude=Number(e.latitude)),null!=e.longitude&&(t.longitude=Number(e.longitude)),t)},_t.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.latitude=0,o.longitude=0),null!=e.latitude&&e.hasOwnProperty("latitude")&&(o.latitude=t.json&&!isFinite(e.latitude)?String(e.latitude):e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&(o.longitude=t.json&&!isFinite(e.longitude)?String(e.longitude):e.longitude),o},_t.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_t.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.type.LatLng"},_t),t),L.longrunning=((n={}).Operations=((Lt.prototype=Object.create(r.rpc.Service.prototype)).constructor=Lt,Object.defineProperty(Lt.prototype.listOperations=function e(t,o){return this.rpcCall(e,s.google.longrunning.ListOperationsRequest,s.google.longrunning.ListOperationsResponse,t,o)},"name",{value:"ListOperations"}),Object.defineProperty(Lt.prototype.getOperation=function e(t,o){return this.rpcCall(e,s.google.longrunning.GetOperationRequest,s.google.longrunning.Operation,t,o)},"name",{value:"GetOperation"}),Object.defineProperty(Lt.prototype.deleteOperation=function e(t,o){return this.rpcCall(e,s.google.longrunning.DeleteOperationRequest,s.google.protobuf.Empty,t,o)},"name",{value:"DeleteOperation"}),Object.defineProperty(Lt.prototype.cancelOperation=function e(t,o){return this.rpcCall(e,s.google.longrunning.CancelOperationRequest,s.google.protobuf.Empty,t,o)},"name",{value:"CancelOperation"}),Object.defineProperty(Lt.prototype.waitOperation=function e(t,o){return this.rpcCall(e,s.google.longrunning.WaitOperationRequest,s.google.longrunning.Operation,t,o)},"name",{value:"WaitOperation"}),Lt),n.Operation=(Ft.prototype.name="",Ft.prototype.metadata=null,Ft.prototype.done=!1,Ft.prototype.error=null,Ft.prototype.response=null,Object.defineProperty(Ft.prototype,"result",{get:a.oneOfGetter(o=["error","response"]),set:a.oneOfSetter(o)}),Ft.fromObject=function(e){if(e instanceof s.google.longrunning.Operation)return e;var t=new s.google.longrunning.Operation;if(null!=e.name&&(t.name=String(e.name)),null!=e.metadata){if("object"!=typeof e.metadata)throw TypeError(".google.longrunning.Operation.metadata: object expected");t.metadata=s.google.protobuf.Any.fromObject(e.metadata)}if(null!=e.done&&(t.done=Boolean(e.done)),null!=e.error){if("object"!=typeof e.error)throw TypeError(".google.longrunning.Operation.error: object expected");t.error=s.google.rpc.Status.fromObject(e.error)}if(null!=e.response){if("object"!=typeof e.response)throw TypeError(".google.longrunning.Operation.response: object expected");t.response=s.google.protobuf.Any.fromObject(e.response)}return t},Ft.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.metadata=null,o.done=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.metadata&&e.hasOwnProperty("metadata")&&(o.metadata=s.google.protobuf.Any.toObject(e.metadata,t)),null!=e.done&&e.hasOwnProperty("done")&&(o.done=e.done),null!=e.error&&e.hasOwnProperty("error")&&(o.error=s.google.rpc.Status.toObject(e.error,t),t.oneofs)&&(o.result="error"),null!=e.response&&e.hasOwnProperty("response")&&(o.response=s.google.protobuf.Any.toObject(e.response,t),t.oneofs)&&(o.result="response"),o},Ft.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ft.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.Operation"},Ft),n.GetOperationRequest=(Ut.prototype.name="",Ut.fromObject=function(e){var t;return e instanceof s.google.longrunning.GetOperationRequest?e:(t=new s.google.longrunning.GetOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},Ut.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Ut.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ut.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.GetOperationRequest"},Ut),n.ListOperationsRequest=(Bt.prototype.name="",Bt.prototype.filter="",Bt.prototype.pageSize=0,Bt.prototype.pageToken="",Bt.fromObject=function(e){var t;return e instanceof s.google.longrunning.ListOperationsRequest?e:(t=new s.google.longrunning.ListOperationsRequest,null!=e.name&&(t.name=String(e.name)),null!=e.filter&&(t.filter=String(e.filter)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),t)},Bt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.filter="",o.pageSize=0,o.pageToken="",o.name=""),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=e.filter),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Bt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Bt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.ListOperationsRequest"},Bt),n.ListOperationsResponse=(Mt.prototype.operations=a.emptyArray,Mt.prototype.nextPageToken="",Mt.fromObject=function(e){if(e instanceof s.google.longrunning.ListOperationsResponse)return e;var t=new s.google.longrunning.ListOperationsResponse;if(e.operations){if(!Array.isArray(e.operations))throw TypeError(".google.longrunning.ListOperationsResponse.operations: array expected");t.operations=[];for(var o=0;o<e.operations.length;++o){if("object"!=typeof e.operations[o])throw TypeError(".google.longrunning.ListOperationsResponse.operations: object expected");t.operations[o]=s.google.longrunning.Operation.fromObject(e.operations[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},Mt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.operations=[]),t.defaults&&(o.nextPageToken=""),e.operations&&e.operations.length){o.operations=[];for(var r=0;r<e.operations.length;++r)o.operations[r]=s.google.longrunning.Operation.toObject(e.operations[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},Mt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Mt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.ListOperationsResponse"},Mt),n.CancelOperationRequest=(Vt.prototype.name="",Vt.fromObject=function(e){var t;return e instanceof s.google.longrunning.CancelOperationRequest?e:(t=new s.google.longrunning.CancelOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},Vt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Vt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Vt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.CancelOperationRequest"},Vt),n.DeleteOperationRequest=(Gt.prototype.name="",Gt.fromObject=function(e){var t;return e instanceof s.google.longrunning.DeleteOperationRequest?e:(t=new s.google.longrunning.DeleteOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},Gt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Gt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Gt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.DeleteOperationRequest"},Gt),n.WaitOperationRequest=(Jt.prototype.name="",Jt.prototype.timeout=null,Jt.fromObject=function(e){if(e instanceof s.google.longrunning.WaitOperationRequest)return e;var t=new s.google.longrunning.WaitOperationRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.timeout){if("object"!=typeof e.timeout)throw TypeError(".google.longrunning.WaitOperationRequest.timeout: object expected");t.timeout=s.google.protobuf.Duration.fromObject(e.timeout)}return t},Jt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.timeout=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.timeout&&e.hasOwnProperty("timeout")&&(o.timeout=s.google.protobuf.Duration.toObject(e.timeout,t)),o},Jt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Jt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.WaitOperationRequest"},Jt),n.OperationInfo=(Yt.prototype.responseType="",Yt.prototype.metadataType="",Yt.fromObject=function(e){var t;return e instanceof s.google.longrunning.OperationInfo?e:(t=new s.google.longrunning.OperationInfo,null!=e.responseType&&(t.responseType=String(e.responseType)),null!=e.metadataType&&(t.metadataType=String(e.metadataType)),t)},Yt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.responseType="",o.metadataType=""),null!=e.responseType&&e.hasOwnProperty("responseType")&&(o.responseType=e.responseType),null!=e.metadataType&&e.hasOwnProperty("metadataType")&&(o.metadataType=e.metadataType),o},Yt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Yt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.OperationInfo"},Yt),n),L.rpc=((i={}).Status=(qt.prototype.code=0,qt.prototype.message="",qt.prototype.details=a.emptyArray,qt.fromObject=function(e){if(e instanceof s.google.rpc.Status)return e;var t=new s.google.rpc.Status;if(null!=e.code&&(t.code=0|e.code),null!=e.message&&(t.message=String(e.message)),e.details){if(!Array.isArray(e.details))throw TypeError(".google.rpc.Status.details: array expected");t.details=[];for(var o=0;o<e.details.length;++o){if("object"!=typeof e.details[o])throw TypeError(".google.rpc.Status.details: object expected");t.details[o]=s.google.protobuf.Any.fromObject(e.details[o])}}return t},qt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.details=[]),t.defaults&&(o.code=0,o.message=""),null!=e.code&&e.hasOwnProperty("code")&&(o.code=e.code),null!=e.message&&e.hasOwnProperty("message")&&(o.message=e.message),e.details&&e.details.length){o.details=[];for(var r=0;r<e.details.length;++r)o.details[r]=s.google.protobuf.Any.toObject(e.details[r],t)}return o},qt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},qt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.rpc.Status"},qt),i),L),s});