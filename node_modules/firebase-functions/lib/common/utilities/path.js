"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.joinPath = exports.pathParts = exports.normalizePath = void 0;
/** @hidden
 * Removes leading and trailing slashes from a path.
 *
 * @param path A path to normalize, in POSIX format.
 */
function normalizePath(path) {
    if (!path) {
        return "";
    }
    return path.replace(/^\//, "").replace(/\/$/, "");
}
exports.normalizePath = normalizePath;
/**
 * Normalizes a given path and splits it into an array of segments.
 *
 * @param path A path to split, in POSIX format.
 */
function pathParts(path) {
    if (!path || path === "" || path === "/") {
        return [];
    }
    return normalizePath(path).split("/");
}
exports.pathParts = pathParts;
/**
 * Normalizes given paths and joins these together using a POSIX separator.
 *
 * @param base A first path segment, in POSIX format.
 * @param child A second path segment, in POSIX format.
 */
function joinPath(base, child) {
    return pathParts(base).concat(pathParts(child)).join("/");
}
exports.joinPath = joinPath;
