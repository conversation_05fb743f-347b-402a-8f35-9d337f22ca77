{"name": "firebase-functions", "version": "4.9.0", "description": "Firebase SDK for Cloud Functions", "keywords": ["firebase", "functions", "google", "cloud"], "homepage": "https://github.com/firebase/firebase-functions#readme", "bugs": {"url": "https://github.com/firebase/firebase-functions/issues"}, "repository": {"type": "git", "url": "https://github.com/firebase/firebase-functions.git"}, "license": "MIT", "author": "Firebase Team", "files": ["lib", "protos"], "main": "lib/v1/index.js", "bin": {"firebase-functions": "./lib/bin/firebase-functions.js"}, "types": "lib/v1/index.d.ts", "exports": {".": "./lib/v1/index.js", "./logger/compat": "./lib/logger/compat.js", "./logger": "./lib/logger/index.js", "./params": "./lib/params/index.js", "./v1": "./lib/v1/index.js", "./v1/analytics": "./lib/v1/providers/analytics.js", "./v1/auth": "./lib/v1/providers/auth.js", "./v1/database": "./lib/v1/providers/database.js", "./v1/firestore": "./lib/v1/providers/firestore.js", "./v1/https": "./lib/v1/providers/https.js", "./v1/pubsub": "./lib/v1/providers/pubsub.js", "./v1/remoteConfig": "./lib/v1/providers/remoteConfig.js", "./v1/storage": "./lib/v1/providers/storage.js", "./v1/tasks": "./lib/v1/providers/tasks.js", "./v1/testLab": "./lib/v1/providers/testLab.js", "./v2": "./lib/v2/index.js", "./v2/core": "./lib/v2/core.js", "./v2/options": "./lib/v2/options.js", "./v2/https": "./lib/v2/providers/https.js", "./v2/pubsub": "./lib/v2/providers/pubsub.js", "./v2/storage": "./lib/v2/providers/storage.js", "./v2/tasks": "./lib/v2/providers/tasks.js", "./v2/alerts": "./lib/v2/providers/alerts/index.js", "./v2/alerts/appDistribution": "./lib/v2/providers/alerts/appDistribution.js", "./v2/alerts/billing": "./lib/v2/providers/alerts/billing.js", "./v2/alerts/crashlytics": "./lib/v2/providers/alerts/crashlytics.js", "./v2/alerts/performance": "./lib/v2/providers/alerts/performance.js", "./v2/eventarc": "./lib/v2/providers/eventarc.js", "./v2/identity": "./lib/v2/providers/identity.js", "./v2/database": "./lib/v2/providers/database.js", "./v2/scheduler": "./lib/v2/providers/scheduler.js", "./v2/remoteConfig": "./lib/v2/providers/remoteConfig.js", "./v2/testLab": "./lib/v2/providers/testLab.js", "./v2/firestore": "./lib/v2/providers/firestore.js"}, "typesVersions": {"*": {"logger": ["lib/logger"], "logger/compat": ["lib/logger/compat"], "params": ["lib/params"], "v1": ["lib/v1"], "v1/analytics": ["lib/v1/providers/analytics"], "v1/auth": ["lib/v1/providers/auth"], "v1/database": ["lib/v1/privders/database"], "v1/firestore": ["lib/v1/providers/firestore"], "v1/https": ["./lib/v1/providers/https"], "v1/pubsub": ["lib/v1/providers/pubsub"], "v1/remoteConfig": ["lib/v1/providers/remoteConfig"], "v1/storage": ["lib/v1/providers/storage"], "v1/tasks": ["lib/v1/providers/tasks"], "v1/testLab": ["lib/v1/providers/testLab"], "v2": ["lib/v2"], "v2/core": ["lib/v2/core"], "v2/alerts": ["lib/v2/providers/alerts"], "v2/alerts/appDistribution": ["lib/v2/providers/alerts/appDistribution"], "v2/alerts/billing": ["lib/v2/providers/alerts/billing"], "v2/alerts/crashlytics": ["lib/v2/providers/alerts/crashlytics"], "v2/alerts/performance": ["lib/v2/providers/alerts/performance"], "v2/base": ["lib/v2/base"], "v2/database": ["lib/v2/providers/database"], "v2/eventarc": ["lib/v2/providers/eventarc"], "v2/identity": ["lib/v2/providers/identity"], "v2/options": ["lib/v2/options"], "v2/https": ["lib/v2/providers/https"], "v2/pubsub": ["lib/v2/providers/pubsub"], "v2/storage": ["lib/v2/providers/storage"], "v2/tasks": ["lib/v2/providers/tasks"], "v2/scheduler": ["lib/v2/providers/scheduler"], "v2/remoteConfig": ["lib/v2/providers/remoteConfig"], "v2/testLab": ["lib/v2/providers/testLab"], "v2/firestore": ["lib/v2/providers/firestore"]}}, "publishConfig": {"registry": "https://wombat-dressing-room.appspot.com"}, "scripts": {"docgen:v1:extract": "api-extractor run -c docgen/api-extractor.v1.json --local", "docgen:v1:toc": "ts-node docgen/toc.ts --input docgen/v1 --output docgen/v1/markdown/toc --path /docs/reference/functions", "docgen:v1:gen": "api-documenter-fire markdown -i docgen/v1 -o docgen/v1/markdown --project functions && npm run docgen:v1:toc", "docgen:v1": "npm run build && npm run docgen:v1:extract && npm run docgen:v1:gen", "docgen:v2:extract": "api-extractor run -c docgen/api-extractor.v2.json --local", "docgen:v2:toc": "ts-node docgen/toc.ts --input docgen/v2 --output docgen/v2/markdown/toc --path /docs/reference/functions/2nd-gen/node", "docgen:v2:gen": "api-documenter-fire markdown -i docgen/v2 -o docgen/v2/markdown --project functions && npm run docgen:v2:toc", "docgen:v2": "npm run build && npm run docgen:v2:extract && npm run docgen:v2:gen", "build:pack": "rm -rf lib && npm install && tsc -p tsconfig.release.json && npm pack", "build:release": "npm ci --production && npm install --no-save typescript firebase-admin && tsc -p tsconfig.release.json", "build": "tsc -p tsconfig.release.json", "build:watch": "npm run build -- -w", "format": "npm run format:ts && npm run format:other", "format:other": "npm run lint:other -- --write", "format:ts": "npm run lint:ts -- --fix --quiet", "lint": "npm run lint:ts && npm run lint:other", "lint:other": "prettier --check '**/*.{md,yaml,yml}'", "lint:quiet": "npm run lint:ts -- --quiet && npm run lint:other", "lint:ts": "eslint --config .eslintrc.js --ext .ts,.js .", "test": "mocha --file ./mocha/setup.ts \"spec/**/*.spec.ts\"", "test:bin": "./scripts/bin-test/run.sh", "test:postmerge": "./integration_test/run_tests.sh"}, "dependencies": {"@types/cors": "^2.8.5", "@types/express": "4.17.3", "cors": "^2.8.5", "express": "^4.17.1", "protobufjs": "^7.2.2"}, "devDependencies": {"@firebase/api-documenter": "^0.2.0", "@microsoft/api-documenter": "^7.13.45", "@microsoft/api-extractor": "^7.18.7", "@types/chai": "^4.1.7", "@types/chai-as-promised": "^7.1.0", "@types/jsonwebtoken": "^9.0.0", "@types/mocha": "^5.2.7", "@types/mock-require": "^2.0.0", "@types/nock": "^10.0.3", "@types/node": "^14.18.24", "@types/node-fetch": "^3.0.3", "@types/sinon": "^7.0.13", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "api-extractor-model-me": "^0.1.1", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "child-process-promise": "^2.2.1", "eslint": "^8.6.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jsdoc": "^39.2.9", "eslint-plugin-prettier": "^4.0.0", "firebase-admin": "^12.0.0", "js-yaml": "^3.13.1", "jsdom": "^16.2.1", "jsonwebtoken": "^9.0.0", "jwk-to-pem": "^2.0.5", "mocha": "^10.2.0", "mock-require": "^3.0.3", "mz": "^2.7.0", "nock": "^13.2.9", "node-fetch": "^2.6.7", "portfinder": "^1.0.28", "prettier": "^2.7.1", "protobufjs-cli": "^1.1.1", "semver": "^7.3.5", "sinon": "^7.3.2", "ts-node": "^10.4.0", "typescript": "^4.3.5", "yargs": "^15.3.1"}, "peerDependencies": {"firebase-admin": "^10.0.0 || ^11.0.0 || ^12.0.0"}, "engines": {"node": ">=14.10.0"}}