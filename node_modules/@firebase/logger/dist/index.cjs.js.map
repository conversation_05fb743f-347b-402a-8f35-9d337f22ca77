{"version": 3, "file": "index.cjs.js", "sources": ["../src/logger.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n"], "names": ["LogLevel", "__spread<PERSON><PERSON>y"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;AAeG;;AAuBH;;AAEG;AACI,IAAM,SAAS,GAAa,EAAE,CAAC;AAEtC;;;;;;;;;;AAUG;AACSA,0BAOX;AAPD,CAAA,UAAY,QAAQ,EAAA;AAClB,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACR,CAAC,EAPWA,gBAAQ,KAARA,gBAAQ,GAOnB,EAAA,CAAA,CAAA,CAAA;AAED,IAAM,iBAAiB,GAA0C;IAC/D,OAAO,EAAEA,gBAAQ,CAAC,KAAK;IACvB,SAAS,EAAEA,gBAAQ,CAAC,OAAO;IAC3B,MAAM,EAAEA,gBAAQ,CAAC,IAAI;IACrB,MAAM,EAAEA,gBAAQ,CAAC,IAAI;IACrB,OAAO,EAAEA,gBAAQ,CAAC,KAAK;IACvB,QAAQ,EAAEA,gBAAQ,CAAC,MAAM;CAC1B,CAAC;AAEF;;AAEG;AACH,IAAM,eAAe,GAAaA,gBAAQ,CAAC,IAAI,CAAC;AAahD;;;;;AAKG;AACH,IAAM,aAAa,IAAA,EAAA,GAAA,EAAA;AACjB,IAAA,EAAA,CAACA,gBAAQ,CAAC,KAAK,CAAA,GAAG,KAAK;AACvB,IAAA,EAAA,CAACA,gBAAQ,CAAC,OAAO,CAAA,GAAG,KAAK;AACzB,IAAA,EAAA,CAACA,gBAAQ,CAAC,IAAI,CAAA,GAAG,MAAM;AACvB,IAAA,EAAA,CAACA,gBAAQ,CAAC,IAAI,CAAA,GAAG,MAAM;AACvB,IAAA,EAAA,CAACA,gBAAQ,CAAC,KAAK,CAAA,GAAG,OAAO;OAC1B,CAAC;AAEF;;;;AAIG;AACH,IAAM,iBAAiB,GAAe,UAAC,QAAQ,EAAE,OAAO,EAAA;IAAE,IAAO,IAAA,GAAA,EAAA,CAAA;SAAP,IAAO,EAAA,GAAA,CAAA,EAAP,EAAO,GAAA,SAAA,CAAA,MAAA,EAAP,EAAO,EAAA,EAAA;QAAP,IAAO,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AAC/D,IAAA,IAAI,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE;QAC/B,OAAO;AACR,KAAA;IACD,IAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;AACrC,IAAA,IAAM,MAAM,GAAG,aAAa,CAAC,OAAqC,CAAC,CAAC;AACpE,IAAA,IAAI,MAAM,EAAE;AACV,QAAA,OAAO,CAAC,MAA2C,CAAC,CAAA,KAAA,CAApD,OAAO,EACLC,mBAAA,CAAA,CAAA,GAAA,CAAA,MAAA,CAAI,GAAG,EAAA,KAAA,CAAA,CAAA,MAAA,CAAM,QAAQ,CAAC,IAAI,EAAG,GAAA,CAAA,CAAA,EAC1B,IAAI,EACP,KAAA,CAAA,CAAA,CAAA;AACH,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CACb,qEAA8D,OAAO,EAAA,GAAA,CAAG,CACzE,CAAC;AACH,KAAA;AACH,CAAC,CAAC;AAEF,IAAA,MAAA,kBAAA,YAAA;AACE;;;;;AAKG;AACH,IAAA,SAAA,MAAA,CAAmB,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AAO/B;;AAEG;QACK,IAAS,CAAA,SAAA,GAAG,eAAe,CAAC;AAkBpC;;;AAGG;QACK,IAAW,CAAA,WAAA,GAAe,iBAAiB,CAAC;AAWpD;;AAEG;QACK,IAAe,CAAA,eAAA,GAAsB,IAAI,CAAC;AA7ChD;;AAEG;AACH,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACtB;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,MAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAAZ,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;AAED,QAAA,GAAA,EAAA,UAAa,GAAa,EAAA;AACxB,YAAA,IAAI,EAAE,GAAG,IAAID,gBAAQ,CAAC,EAAE;AACtB,gBAAA,MAAM,IAAI,SAAS,CAAC,0BAAkB,GAAG,EAAA,2BAAA,CAA4B,CAAC,CAAC;AACxE,aAAA;AACD,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;SACtB;;;AAPA,KAAA,CAAA,CAAA;;IAUD,MAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,GAA8B,EAAA;AACxC,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACzE,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,MAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AAAd,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,WAAW,CAAC;SACzB;AACD,QAAA,GAAA,EAAA,UAAe,GAAe,EAAA;AAC5B,YAAA,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;AAC7B,gBAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;AAC1E,aAAA;AACD,YAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;SACxB;;;AANA,KAAA,CAAA,CAAA;AAYD,IAAA,MAAA,CAAA,cAAA,CAAI,MAAc,CAAA,SAAA,EAAA,gBAAA,EAAA;AAAlB,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;AACD,QAAA,GAAA,EAAA,UAAmB,GAAsB,EAAA;AACvC,YAAA,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;SAC5B;;;AAHA,KAAA,CAAA,CAAA;AAKD;;AAEG;AAEH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QAAM,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACtB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAApB,KAAA,CAAA,IAAI,EAAiBC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,KAAK,CAAK,EAAA,IAAI,SAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAhB,KAAA,CAAA,IAAI,EAAaC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,KAAK,CAAK,EAAA,IAAI,EAAE,KAAA,CAAA,CAAA,CAAA;KACjD,CAAA;AACD,IAAA,MAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA;QAAI,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACpB,QAAA,IAAI,CAAC,eAAe,IAClB,IAAI,CAAC,eAAe,CAApB,KAAA,CAAA,IAAI,EAAiBC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,OAAO,CAAK,EAAA,IAAI,SAAC,CAAC;AACxD,QAAA,IAAI,CAAC,WAAW,CAAhB,KAAA,CAAA,IAAI,EAAaC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,OAAO,CAAK,EAAA,IAAI,EAAE,KAAA,CAAA,CAAA,CAAA;KACnD,CAAA;AACD,IAAA,MAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QAAK,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACrB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAApB,KAAA,CAAA,IAAI,EAAiBC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,IAAI,CAAK,EAAA,IAAI,SAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAhB,KAAA,CAAA,IAAI,EAAaC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,IAAI,CAAK,EAAA,IAAI,EAAE,KAAA,CAAA,CAAA,CAAA;KAChD,CAAA;AACD,IAAA,MAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QAAK,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACrB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAApB,KAAA,CAAA,IAAI,EAAiBC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,IAAI,CAAK,EAAA,IAAI,SAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAhB,KAAA,CAAA,IAAI,EAAaC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,IAAI,CAAK,EAAA,IAAI,EAAE,KAAA,CAAA,CAAA,CAAA;KAChD,CAAA;AACD,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QAAM,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACtB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAApB,KAAA,CAAA,IAAI,EAAiBC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,KAAK,CAAK,EAAA,IAAI,SAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAhB,KAAA,CAAA,IAAI,EAAaC,mBAAA,CAAA,CAAA,IAAI,EAAED,gBAAQ,CAAC,KAAK,CAAK,EAAA,IAAI,EAAE,KAAA,CAAA,CAAA,CAAA;KACjD,CAAA;IACH,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAEK,SAAU,WAAW,CAAC,KAAgC,EAAA;AAC1D,IAAA,SAAS,CAAC,OAAO,CAAC,UAAA,IAAI,EAAA;AACpB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL,CAAC;AAEe,SAAA,iBAAiB,CAC/B,WAA+B,EAC/B,OAAoB,EAAA;4BAET,QAAQ,EAAA;QACjB,IAAI,cAAc,GAAoB,IAAI,CAAC;AAC3C,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;AAC5B,YAAA,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACnD,SAAA;QACD,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;AAChC,SAAA;AAAM,aAAA;AACL,YAAA,QAAQ,CAAC,cAAc,GAAG,UACxB,QAAgB,EAChB,KAAe,EAAA;gBACf,IAAkB,IAAA,GAAA,EAAA,CAAA;qBAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;oBAAlB,IAAkB,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;gBAElB,IAAM,OAAO,GAAG,IAAI;qBACjB,GAAG,CAAC,UAAA,GAAG,EAAA;oBACN,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,wBAAA,OAAO,IAAI,CAAC;AACb,qBAAA;AAAM,yBAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAClC,wBAAA,OAAO,GAAG,CAAC;AACZ,qBAAA;yBAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;AAC9D,wBAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACvB,qBAAA;yBAAM,IAAI,GAAG,YAAY,KAAK,EAAE;wBAC/B,OAAO,GAAG,CAAC,OAAO,CAAC;AACpB,qBAAA;AAAM,yBAAA;wBACL,IAAI;AACF,4BAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC5B,yBAAA;AAAC,wBAAA,OAAO,OAAO,EAAE;AAChB,4BAAA,OAAO,IAAI,CAAC;AACb,yBAAA;AACF,qBAAA;AACH,iBAAC,CAAC;qBACD,MAAM,CAAC,UAAA,GAAG,EAAA,EAAI,OAAA,GAAG,CAAA,EAAA,CAAC;qBAClB,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,gBAAA,IAAI,KAAK,KAAK,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,KAAA,CAAA,GAAA,cAAc,GAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClD,oBAAA,WAAW,CAAC;AACV,wBAAA,KAAK,EAAEA,gBAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAoB;AACtD,wBAAA,OAAO,EAAA,OAAA;AACP,wBAAA,IAAI,EAAA,IAAA;wBACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;AACpB,qBAAA,CAAC,CAAC;AACJ,iBAAA;AACH,aAAC,CAAC;AACH,SAAA;;AA1CH,IAAA,KAAuB,UAAS,EAAT,WAAA,GAAA,SAAS,EAAT,EAAA,GAAA,WAAA,CAAA,MAAS,EAAT,EAAS,EAAA,EAAA;AAA3B,QAAA,IAAM,QAAQ,GAAA,WAAA,CAAA,EAAA,CAAA,CAAA;gBAAR,QAAQ,CAAA,CAAA;AA2ClB,KAAA;AACH;;;;;;"}