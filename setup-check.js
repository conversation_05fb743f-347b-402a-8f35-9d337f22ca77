#!/usr/bin/env node

/**
 * Setup Verification Script for Credit Chakra Platform
 * 
 * Checks prerequisites and configuration before running the utilities:
 * - Node.js version compatibility
 * - Required dependencies installation
 * - Firebase service account key
 * - Environment configuration
 * - Directory structure
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

class SetupChecker {
    constructor() {
        this.checks = [];
        this.errors = [];
        this.warnings = [];
    }

    /**
     * Check Node.js version
     */
    checkNodeVersion() {
        const requiredVersion = '16.0.0';
        const currentVersion = process.version.substring(1); // Remove 'v' prefix
        
        const isCompatible = this.compareVersions(currentVersion, requiredVersion) >= 0;
        
        this.checks.push({
            name: 'Node.js Version',
            status: isCompatible ? 'PASS' : 'FAIL',
            message: `Current: ${process.version}, Required: >= v${requiredVersion}`,
            critical: true
        });

        if (!isCompatible) {
            this.errors.push(`Node.js version ${process.version} is not supported. Please upgrade to v${requiredVersion} or higher.`);
        }
    }

    /**
     * Check if dependencies are installed
     */
    checkDependencies() {
        const packageJsonPath = path.join(__dirname, 'package.json');
        const nodeModulesPath = path.join(__dirname, 'node_modules');
        
        const packageJsonExists = fs.existsSync(packageJsonPath);
        const nodeModulesExists = fs.existsSync(nodeModulesPath);
        
        this.checks.push({
            name: 'Dependencies Installation',
            status: (packageJsonExists && nodeModulesExists) ? 'PASS' : 'FAIL',
            message: nodeModulesExists ? 'Dependencies installed' : 'Run: npm install',
            critical: true
        });

        if (!nodeModulesExists) {
            this.errors.push('Dependencies not installed. Run "npm install" to install required packages.');
        }

        // Check specific critical dependencies
        const criticalDeps = ['firebase-admin', 'joi', 'winston', 'uuid', 'dotenv'];
        for (const dep of criticalDeps) {
            const depPath = path.join(__dirname, 'node_modules', dep);
            const exists = fs.existsSync(depPath);
            
            this.checks.push({
                name: `Dependency: ${dep}`,
                status: exists ? 'PASS' : 'FAIL',
                message: exists ? 'Installed' : 'Missing',
                critical: true
            });

            if (!exists) {
                this.errors.push(`Critical dependency missing: ${dep}`);
            }
        }
    }

    /**
     * Check environment configuration
     */
    checkEnvironmentConfig() {
        const envPath = path.join(__dirname, '.env');
        const envExists = fs.existsSync(envPath);
        
        this.checks.push({
            name: 'Environment File',
            status: envExists ? 'PASS' : 'FAIL',
            message: envExists ? '.env file found' : '.env file missing',
            critical: true
        });

        if (!envExists) {
            this.errors.push('.env file not found. Copy from .env.example and configure.');
            return;
        }

        // Check required environment variables
        const requiredEnvVars = [
            'FIREBASE_PROJECT_ID',
            'FIREBASE_SERVICE_ACCOUNT_KEY_PATH',
            'COLLECTION_MSMES',
            'COLLECTION_RAW_EVENTS',
            'COLLECTION_ALERTS',
            'COLLECTION_CHAKRA_SCORE',
            'COLLECTION_CONSENT_LEDGER'
        ];

        for (const envVar of requiredEnvVars) {
            const value = process.env[envVar];
            const isSet = value && value.trim() !== '';
            
            this.checks.push({
                name: `Environment Variable: ${envVar}`,
                status: isSet ? 'PASS' : 'FAIL',
                message: isSet ? `Set to: ${value}` : 'Not configured',
                critical: true
            });

            if (!isSet) {
                this.errors.push(`Environment variable ${envVar} is not set in .env file.`);
            }
        }
    }

    /**
     * Check Firebase service account key
     */
    checkFirebaseServiceAccount() {
        const serviceAccountPath = path.resolve(__dirname, process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH || '../serviceAccountKey.json');
        const exists = fs.existsSync(serviceAccountPath);
        
        this.checks.push({
            name: 'Firebase Service Account Key',
            status: exists ? 'PASS' : 'FAIL',
            message: exists ? `Found at: ${serviceAccountPath}` : `Not found at: ${serviceAccountPath}`,
            critical: true
        });

        if (!exists) {
            this.errors.push(`Firebase service account key not found at: ${serviceAccountPath}`);
            this.errors.push('1. Download service account key from Firebase Console');
            this.errors.push('2. Save as serviceAccountKey.json in the parent directory');
            this.errors.push('3. Or update FIREBASE_SERVICE_ACCOUNT_KEY_PATH in .env');
        } else {
            // Validate JSON structure
            try {
                const keyContent = fs.readFileSync(serviceAccountPath, 'utf8');
                const keyData = JSON.parse(keyContent);
                
                const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
                const missingFields = requiredFields.filter(field => !keyData[field]);
                
                if (missingFields.length > 0) {
                    this.checks.push({
                        name: 'Service Account Key Validation',
                        status: 'FAIL',
                        message: `Missing fields: ${missingFields.join(', ')}`,
                        critical: true
                    });
                    this.errors.push(`Invalid service account key. Missing fields: ${missingFields.join(', ')}`);
                } else {
                    this.checks.push({
                        name: 'Service Account Key Validation',
                        status: 'PASS',
                        message: `Valid for project: ${keyData.project_id}`,
                        critical: false
                    });
                }
            } catch (error) {
                this.checks.push({
                    name: 'Service Account Key Validation',
                    status: 'FAIL',
                    message: 'Invalid JSON format',
                    critical: true
                });
                this.errors.push('Service account key file is not valid JSON.');
            }
        }
    }

    /**
     * Check directory structure
     */
    checkDirectoryStructure() {
        const requiredDirs = [
            'firestore',
            'firestore/seeds',
            'firestore/rules',
            'scripts',
            'utils',
            'logs'
        ];

        for (const dir of requiredDirs) {
            const dirPath = path.join(__dirname, dir);
            const exists = fs.existsSync(dirPath);
            
            if (dir === 'logs' && !exists) {
                // Create logs directory if it doesn't exist
                try {
                    fs.mkdirSync(dirPath, { recursive: true });
                } catch (error) {
                    // Ignore error, will be caught in the check below
                }
            }
            
            const finalExists = fs.existsSync(dirPath);
            
            this.checks.push({
                name: `Directory: ${dir}`,
                status: finalExists ? 'PASS' : 'FAIL',
                message: finalExists ? 'Exists' : 'Missing',
                critical: dir !== 'logs'
            });

            if (!finalExists && dir !== 'logs') {
                this.errors.push(`Required directory missing: ${dir}`);
            }
        }
    }

    /**
     * Check required files
     */
    checkRequiredFiles() {
        const requiredFiles = [
            'package.json',
            'firestore/init.js',
            'firestore/seeds/seed-all.js',
            'scripts/ingest-event.js',
            'scripts/validate-schema.js',
            'scripts/cleanup-collections.js',
            'utils/logger.js',
            'utils/validators.js'
        ];

        for (const file of requiredFiles) {
            const filePath = path.join(__dirname, file);
            const exists = fs.existsSync(filePath);
            
            this.checks.push({
                name: `File: ${file}`,
                status: exists ? 'PASS' : 'FAIL',
                message: exists ? 'Exists' : 'Missing',
                critical: true
            });

            if (!exists) {
                this.errors.push(`Required file missing: ${file}`);
            }
        }
    }

    /**
     * Compare version strings
     */
    compareVersions(version1, version2) {
        const v1parts = version1.split('.').map(Number);
        const v2parts = version2.split('.').map(Number);
        
        for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
            const v1part = v1parts[i] || 0;
            const v2part = v2parts[i] || 0;
            
            if (v1part > v2part) return 1;
            if (v1part < v2part) return -1;
        }
        
        return 0;
    }

    /**
     * Run all checks
     */
    async runAllChecks() {
        console.log('\n🔍 Credit Chakra Platform - Setup Verification');
        console.log('===============================================\n');

        this.checkNodeVersion();
        this.checkDependencies();
        this.checkEnvironmentConfig();
        this.checkFirebaseServiceAccount();
        this.checkDirectoryStructure();
        this.checkRequiredFiles();

        this.displayResults();
        return this.errors.length === 0;
    }

    /**
     * Display check results
     */
    displayResults() {
        console.log('📋 Setup Check Results:');
        console.log('========================\n');

        // Group checks by status
        const passed = this.checks.filter(check => check.status === 'PASS');
        const failed = this.checks.filter(check => check.status === 'FAIL');

        // Display summary
        console.log(`✅ Passed: ${passed.length}`);
        console.log(`❌ Failed: ${failed.length}`);
        console.log(`📊 Total: ${this.checks.length}\n`);

        // Display failed checks
        if (failed.length > 0) {
            console.log('❌ Failed Checks:');
            failed.forEach(check => {
                const icon = check.critical ? '🚨' : '⚠️';
                console.log(`   ${icon} ${check.name}: ${check.message}`);
            });
            console.log('');
        }

        // Display errors
        if (this.errors.length > 0) {
            console.log('🔧 Required Actions:');
            this.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
            console.log('');
        }

        // Display warnings
        if (this.warnings.length > 0) {
            console.log('⚠️  Warnings:');
            this.warnings.forEach((warning, index) => {
                console.log(`   ${index + 1}. ${warning}`);
            });
            console.log('');
        }

        // Final status
        if (this.errors.length === 0) {
            console.log('🎉 Setup verification completed successfully!');
            console.log('✨ You can now run the Credit Chakra utilities.');
            console.log('\n📚 Next steps:');
            console.log('   1. npm run seed:all          # Seed sample data');
            console.log('   2. npm run validate:schema   # Validate data');
            console.log('   3. npm run ingest:event      # Test event ingestion');
        } else {
            console.log('💥 Setup verification failed!');
            console.log('🔧 Please fix the issues above before proceeding.');
        }
    }
}

// Run setup check if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            const checker = new SetupChecker();
            const success = await checker.runAllChecks();
            process.exit(success ? 0 : 1);
        } catch (error) {
            console.error('\n💥 Fatal error during setup check:', error);
            process.exit(1);
        }
    })();
}

module.exports = SetupChecker;
